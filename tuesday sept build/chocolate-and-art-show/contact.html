<!DOCTYPE html>
<html lang="en">

  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Contact — Chocolate & Art Show Dallas | September 18-19, 2025</title>
    <meta name="description"
      content="Contact Chocolate & Art Show Dallas. Get directions to Lofty Spaces, parking information, and reach out with questions." />

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="assets/images/choco-logo.png" />

    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/tokens.css">
    <link rel="stylesheet" href="assets/css/base.css">
    <link rel="stylesheet" href="assets/css/layout.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/buttons.css">

    <style>
      :root {
        --pink: #ff2ebd;
        --pink-2: #ff7ad6;
        --white: #ffffff;
        --bg: #000;
        --card-bg: #1a1a1a;
        --card-bd: #333;
        --fg: #eef2ff;
        --name: #fff;
      }

      * {
        box-sizing: border-box;
      }

      html,
      body {
        height: 100%;
        margin: 0;
        padding: 0;
      }

      body {
        background: var(--bg);
        color: var(--white);
        font-family: ui-sans-serif, system-ui, -apple-system, "Segoe UI", Roboto, Inter, Arial, sans-serif;
        padding-top: 80px;
      }

      /* Navigation */
      .nav {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        background: rgba(0, 0, 0, 0.9);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .nav-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 2rem;
        max-width: 1200px;
        margin: 0 auto;
      }

      .nav-logo {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--white);
        text-decoration: none;
        font-weight: 800;
      }

      .nav-menu {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
        gap: 2rem;
      }

      .nav-menu a {
        color: var(--white);
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
      }

      .nav-menu a:hover {
        color: var(--pink);
      }

      /* Container */
      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
      }

      /* Contact Header */
      .contact-header {
        text-align: center;
        padding: 4rem 0 2rem;
      }

      .contact-header h1 {
        font-size: clamp(2.5rem, 6vw, 4rem);
        margin-bottom: 1rem;
        color: var(--pink);
      }

      .contact-header p {
        font-size: 1.2rem;
        color: var(--fg);
      }

      /* Contact Grid */
      .contact-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 2rem;
        margin: 3rem 0;
      }

      /* Cards with thicker borders */
      .bwhc-card {
        background: var(--card-bg);
        border: 3px solid var(--card-bd);
        padding: 2rem;
        border-radius: 8px;
      }

      .venue-info h2,
      .contact-info h2 {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
        color: var(--pink);
      }

      .venue-card h3 {
        margin: 0 0 1rem 0;
        color: var(--white);
      }

      .venue-details p {
        margin: 0.5rem 0;
        color: var(--fg);
      }

      .contact-method h3 {
        margin: 0 0 1rem 0;
        color: var(--white);
      }

      .contact-method a {
        color: var(--pink);
        text-decoration: none;
      }

      .contact-method a:hover {
        text-decoration: underline;
      }

      /* Transportation */
      .transportation {
        margin: 4rem 0;
      }

      .transportation h2 {
        font-size: 2rem;
        margin-bottom: 2rem;
        text-align: center;
        color: var(--pink);
      }

      .transport-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
      }

      .transport-option h3 {
        margin: 0 0 1rem 0;
        color: var(--white);
      }

      .transport-option p {
        color: var(--fg);
        line-height: 1.6;
      }

      /* Important Notes */
      .important-notes {
        margin: 4rem 0;
      }

      .important-notes h2 {
        font-size: 2rem;
        margin-bottom: 2rem;
        text-align: center;
        color: var(--pink);
      }

      .notes-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
      }

      .note-card h3 {
        margin: 0 0 1rem 0;
        color: var(--white);
      }

      .note-card p {
        color: var(--fg);
        line-height: 1.6;
      }

      /* CTA Section */
      .contact-cta {
        text-align: center;
        padding: 4rem 0;
        background: var(--card-bg);
        margin: 4rem 0;
        border-radius: 8px;
      }

      .contact-cta h2 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        color: var(--pink);
      }

      .contact-cta p {
        font-size: 1.2rem;
        margin-bottom: 2rem;
        color: var(--fg);
      }

      .cta-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
      }

      .btn {
        display: inline-block;
        padding: 1rem 2rem;
        background: linear-gradient(135deg, var(--pink), var(--pink-2));
        color: var(--white);
        text-decoration: none;
        font-weight: 800;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        border: 2px solid transparent;
        transition: all 0.3s ease;
        min-width: 200px;
        text-align: center;
        border-radius: 4px;
      }

      .btn:hover {
        background: linear-gradient(135deg, var(--pink-2), var(--pink));
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 46, 189, 0.3);
      }

      .btn-secondary {
        background: transparent;
        border: 2px solid var(--pink);
        color: var(--pink);
      }

      .btn-secondary:hover {
        background: var(--pink);
        color: var(--white);
      }

      /* Footer */
      .site-footer {
        background: #0a0a0a;
        color: #eaeaea;
        padding: 24px 16px;
        margin-top: 4rem;
      }

      .footer-inner {
        display: flex;
        justify-content: space-between;
        align-items: center;
        max-width: 1200px;
        margin: 0 auto;
        flex-wrap: wrap;
        gap: 1rem;
      }

      .brand {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        color: inherit;
        text-decoration: none;
        font-weight: 600;
      }

      .footer-nav {
        display: flex;
        gap: 16px;
        flex-wrap: wrap;
      }

      .footer-icon {
        color: #cfcfcf;
        text-decoration: none;
        font-size: .95rem;
      }

      .footer-icon:hover {
        color: #fff;
      }
    </style>
  </head>

  <body>
    <!-- Navigation -->
    <nav class="nav" role="navigation" aria-label="Main navigation">
      <div class="nav-container">
        <a href="index.html" class="nav-logo" aria-label="Chocolate & Art Show home">
          <img src="assets/images/choco-logo.png" alt="Chocolate & Art Show logo" width="40" height="40" />
          <span>Chocolate & Art</span>
        </a>

        <ul class="nav-menu" role="menubar">
          <li role="none"><a href="index.html" role="menuitem">Home</a></li>
          <li role="none"><a href="#about" role="menuitem">About</a></li>
          <li role="none"><a href="events/" role="menuitem">Events & Cities</a></li>
          <li role="none"><a href="events/dallas-tx-2025-09-18-19/" role="menuitem">Tickets</a></li>
          <li role="none"><a href="artists.html" role="menuitem">Artists</a></li>
          <li role="none"><a href="contact.html" role="menuitem" aria-current="page">Contact</a></li>
        </ul>
      </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content">
      <div class="container">
        <section class="contact-header">
          <h1>Contact & Venue Information</h1>
          <p>Everything you need to know about getting to Chocolate & Art Show Dallas</p>
        </section>

        <div class="contact-grid">
          <!-- Venue Information -->
          <section class="venue-info">
            <h2>📍 Venue</h2>
            <div class="venue-card bwhc-card">
              <h3>Lofty Spaces</h3>
              <address>
                Dallas, TX<br />
                United States
              </address>
              <div class="venue-details">
                <p><strong>Event Dates:</strong> September 18-19, 2025</p>
                <p><strong>Doors Open:</strong> 7:00 PM</p>
                <p><strong>Last Entry:</strong> 12:30 AM</p>
                <p><strong>Age Requirement:</strong> 21+ (ID Required)</p>
              </div>
            </div>
          </section>

          <!-- Contact Information -->
          <section class="contact-info">
            <h2>📧 Get in Touch</h2>
            <div class="contact-methods">
              <div class="contact-method bwhc-card">
                <h3>General Inquiries</h3>
                <button class="btn"
                  onclick="openCompose('<EMAIL>', 'General Inquiry', 'Hello, I have a question about the Chocolate & Art Show...')">
                  Contact Us
                </button>
              </div>
            </div>
          </section>
        </div>

        <!-- Transportation -->
        <section class="transportation">
          <h2>🚗 Getting There</h2>
          <div class="transport-grid">
            <div class="transport-option bwhc-card">
              <h3>Driving</h3>
              <p>Parking is available at the venue. We recommend arriving early as spaces are limited.</p>
            </div>

            <div class="transport-option bwhc-card">
              <h3>Rideshare</h3>
              <p>Uber and Lyft are recommended for convenience. The venue is easily accessible for pickup and drop-off.
              </p>
            </div>

            <div class="transport-option bwhc-card">
              <h3>Public Transit</h3>
              <p>Check local Dallas transit options for routes to the venue area.</p>
            </div>
          </div>
        </section>

        <!-- Important Notes -->
        <section class="important-notes">
          <h2>⚠️ Important Information</h2>
          <div class="notes-grid">
            <div class="note-card bwhc-card">
              <h3>ID Required</h3>
              <p>This is a 21+ event. Valid government-issued photo ID is required for entry. No exceptions.</p>
            </div>

            <div class="note-card bwhc-card">
              <h3>No Re-entry</h3>
              <p>Once you leave the venue, re-entry is not permitted. Please plan accordingly.</p>
            </div>

            <div class="note-card bwhc-card">
              <h3>Capacity Limited</h3>
              <p>This is an intimate event with limited capacity. Advance ticket purchase is strongly recommended.</p>
            </div>

            <div class="note-card bwhc-card">
              <h3>Dress Code</h3>
              <p>Come as you are! We encourage creative expression and comfortable attire for an evening of art and
                music.</p>
            </div>
          </div>
        </section>

        <!-- CTA Section -->
        <section class="contact-cta">
          <h2>Ready to Join Us?</h2>
          <p>Secure your spot at Dallas's most immersive art experience.</p>
          <div class="cta-buttons">
            <a href="events/dallas-tx-2025-09-18-19/" class="btn">Get Tickets</a>
            <button class="btn btn-secondary"
              onclick="openCompose('<EMAIL>', 'Artist Application', 'I would like to apply to participate in the Chocolate & Art Show...')">
              Apply to Participate
            </button>
          </div>
        </section>
      </div>
    </main>

    <!-- Footer -->
    <footer class="site-footer">
      <div class="footer-inner">
        <a class="brand" href="index.html" aria-label="Chocolate & Art Show home">
          <img src="assets/images/choco-logo.png" width="24" height="24" alt="C&A" />
          <span>Chocolate & Art Show</span>
        </a>

        <nav class="footer-nav" aria-label="Social Links">
          <a class="footer-icon" href="#" aria-label="Instagram">IG</a>
          <a class="footer-icon" href="#" aria-label="Facebook">FB</a>
          <a class="footer-icon" href="#" aria-label="Email">Email</a>
        </nav>
      </div>
    </footer>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/compose.js"></script>

  </body>

</html>