<!DOCTYPE html>
<html lang="en">

  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Chocolate & Art Show — Dallas | September 18-19, 2025</title>
    <meta name="description"
      content="Immersive art, live music, body painting, and artisan chocolate. Two nights in Dallas at Lofty Spaces. 21+ event featuring local artists and DJs.">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="assets/images/choco-logo.png">

    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/tokens.css">
    <link rel="stylesheet" href="assets/css/base.css">
    <link rel="stylesheet" href="assets/css/layout.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/buttons.css">
    <link rel="stylesheet" href="assets/css/confetti.css">

    <style>
      :root {
        --pink: #ff2ebd;
        --pink-2: #ff7ad6;
        --white: #ffffff;
        --bg: #000;
        --card-bg: #1a1a1a;
        --card-bd: #333;
        --fg: #eef2ff;
        --name: #fff;
      }

      * {
        box-sizing: border-box;
      }

      html,
      body {
        height: 100%;
        margin: 0;
        padding: 0;
      }

      body {
        background: var(--bg);
        color: var(--white);
        font-family: ui-sans-serif, system-ui, -apple-system, "Segoe UI", Roboto, Inter, Arial, sans-serif;
      }

      /* Navigation */
      .nav {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        background: rgba(0, 0, 0, 0.9);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .nav-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 2rem;
        max-width: 1200px;
        margin: 0 auto;
      }

      .nav-logo {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--white);
        text-decoration: none;
        font-weight: 800;
      }

      .nav-menu {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
        gap: 2rem;
      }

      .nav-menu a {
        color: var(--white);
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
      }

      .nav-menu a:hover {
        color: var(--pink);
      }

      /* Hero Section with Neon (No Video) */
      .hero {
        position: relative;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        background: var(--bg);
        padding-top: 80px;
      }

      .hero-content {
        position: relative;
        text-align: center;
        color: var(--white);
        z-index: 10;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 3rem;
        padding: 2rem;
      }

      /* Neon Sign */
      .neon-sign {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: clamp(0.4rem, 2.2vw, 1.25rem);
        text-align: center;
        margin-bottom: 2rem;
      }

      .neon-line {
        position: relative;
        font-family: ui-sans-serif, system-ui, sans-serif;
        font-weight: 900;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        color: var(--white);
        text-shadow:
          0 0 5px var(--pink),
          0 0 10px var(--pink),
          0 0 15px var(--pink),
          0 0 20px var(--pink);
      }

      .line-1 {
        font-size: clamp(2.5rem, 8vw, 6rem);
      }

      .line-2 {
        font-size: clamp(2rem, 6vw, 4.5rem);
      }

      .line-3 {
        font-size: clamp(2.5rem, 8vw, 6rem);
      }

      /* Hero CTAs */
      .hero-cta {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2rem;
      }

      .cta-title {
        font-size: clamp(1.5rem, 4vw, 2.5rem);
        font-weight: 800;
        margin: 0;
        color: var(--white);
      }

      .ticket-buttons {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        align-items: center;
      }

      .btn-textured {
        display: inline-block;
        padding: 1rem 2rem;
        background: linear-gradient(135deg, var(--pink), var(--pink-2));
        color: var(--white);
        text-decoration: none;
        font-weight: 800;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        border: 2px solid transparent;
        transition: all 0.3s ease;
        min-width: 280px;
        text-align: center;
      }

      .btn-textured:hover {
        background: linear-gradient(135deg, var(--pink-2), var(--pink));
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 46, 189, 0.3);
      }

      @media (min-width: 768px) {
        .ticket-buttons {
          flex-direction: row;
          gap: 2rem;
        }
      }

      /* RGB Wall Styles */
      .rgb-wall {
        isolation: isolate;
        min-height: 58vh;
        display: grid;
        place-items: center;
        gap: 2rem;
        padding: 8vh 4vw;
        background: var(--bg);
      }

      .rgb-hero {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: clamp(.4rem, 2vw, 1.2rem);
        justify-content: center;
      }

      .rgb-plus,
      .rgb-eq {
        color: #8ea3b3;
        font-weight: 800;
        font-size: clamp(2rem, 8vw, 6rem);
      }

      .rgb-word {
        position: relative;
        display: inline-block;
        font-weight: 900;
        text-transform: uppercase;
        letter-spacing: -.02em;
        line-height: .85;
        color: #eaeaea;
        font-size: clamp(3rem, 14vw, 12rem);
      }

      .rgb-word::before,
      .rgb-word::after {
        content: attr(data-txt);
        position: absolute;
        inset: 0;
        z-index: -1;
      }

      .rgb-word::before {
        transform: translate(.18em, .06em);
        color: #e53935;
        opacity: .8;
      }

      .rgb-word::after {
        transform: translate(-.14em, .08em);
        color: #03a9f4;
        opacity: .75;
      }

      /* Container and Layout */
      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 2rem;
      }

      /* About Section */
      .about-section {
        padding: 4rem 0;
        background: var(--card-bg);
      }

      .about-section h2 {
        font-size: clamp(2rem, 5vw, 3rem);
        margin-bottom: 1rem;
        text-align: center;
      }

      .about-section p {
        font-size: 1.2rem;
        line-height: 1.6;
        text-align: center;
        max-width: 600px;
        margin: 0 auto;
      }

      /* CTA Bar */
      .cta-bar {
        padding: 4rem 0;
        background: var(--bg);
      }

      .cta-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
      }

      .cta-item {
        background: var(--card-bg);
        padding: 2rem;
        border: 2px solid var(--card-bd);
        text-align: center;
      }

      .cta-item h3 {
        margin: 0 0 1rem 0;
        color: var(--pink);
      }

      .cta-item p {
        margin: 0 0 1.5rem 0;
        color: var(--fg);
      }

      /* Cities Section */
      .cities-section {
        padding: 4rem 0;
        background: var(--card-bg);
      }

      .cities-section h2 {
        font-size: clamp(2rem, 5vw, 3rem);
        margin-bottom: 2rem;
        text-align: center;
      }

      .cities-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
      }

      .city-card {
        background: var(--bg);
        padding: 2rem;
        border: 2px solid var(--card-bd);
        text-align: center;
      }

      .city-card h3 {
        margin: 0 0 1rem 0;
        color: var(--pink);
      }

      .city-card p {
        margin: 0.5rem 0;
        color: var(--fg);
      }

      /* Footer Styles */
      .site-footer {
        background: #0a0a0a;
        color: #eaeaea;
        padding: 24px 16px;
      }

      .footer-inner {
        display: grid;
        grid-template-columns: auto 1fr auto auto;
        gap: 20px;
        align-items: center;
        max-width: 1200px;
        margin: 0 auto;
      }

      .footer-inner>* {
        min-width: 0;
      }

      .footer-nav,
      .footer-social {
        display: flex;
        gap: 16px;
        flex-wrap: wrap;
      }

      .footer-nav a,
      .footer-social a {
        color: #cfcfcf;
        text-decoration: none;
        font-size: .95rem;
      }

      .footer-nav a:hover,
      .footer-social a:hover {
        color: #fff;
      }

      .brand-ft {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        color: inherit;
        text-decoration: none;
        font-weight: 600;
      }

      .brand-ft span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .footer-legal {
        max-width: 1200px;
        margin: 10px auto 0;
        padding-top: 8px;
        border-top: 1px solid #ffffff15;
        color: #bdbdbd;
        font-size: .85rem;
        text-align: center;
      }

      /* Button Styles */
      .btn-modern {
        position: relative;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 44px;
        background: linear-gradient(180deg, #2a2a2a, #1a1a1a);
        border: 1px solid rgba(255, 255, 255, 0.12);
        border-radius: 8px;
        color: #fff;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .btn-modern:hover {
        box-shadow: 0 16px 28px rgba(0, 0, 0, .4), 0 0 0 1px rgba(255, 255, 255, .08), inset 0 1px 0 rgba(255, 255, 255, .1), inset 0 -1px 0 rgba(0, 0, 0, .65);
      }

      .btn-modern:active {
        transform: translateY(1px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, .35), 0 0 0 1px rgba(255, 255, 255, .1), inset 0 1px 0 rgba(255, 255, 255, .06), inset 0 -1px 0 rgba(0, 0, 0, .5);
      }

      .btn-modern__inner {
        position: relative;
        z-index: 2;
        padding: 0 18px;
        font-weight: 800;
        letter-spacing: .02em;
      }

      .btn-modern::after {
        content: "";
        position: absolute;
        inset: 0;
        background: linear-gradient(180deg, rgba(255, 255, 255, .06), transparent 40% 60%, rgba(255, 255, 255, .02));
        mix-blend-mode: screen;
        pointer-events: none;
      }

      /* Confetti Canvas */
      .confetti-canvas {
        position: fixed;
        inset: 0;
        pointer-events: none;
        z-index: 9999;
        display: block;
      }

      /* Screen Reader Only */
      .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
      }

      @media (prefers-reduced-motion: reduce) {

        .btn-modern,
        .btn-textured {
          transition: none;
        }
      }
    </style>
  </head>

  <body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only">Skip to main content</a>

    <!-- Navigation -->
    <nav class="nav" role="navigation" aria-label="Main navigation">
      <div class="nav-container">
        <a href="index.html" class="nav-logo" aria-label="Chocolate & Art Show home">
          <img src="assets/images/choco-logo.png" alt="Chocolate & Art Show logo" width="40" height="40">
          <span>Chocolate & Art</span>
        </a>

        <ul class="nav-menu" role="menubar">
          <li role="none">
            <a href="index.html" role="menuitem">Home</a>
          </li>
          <li role="none">
            <a href="#about" role="menuitem">About</a>
          </li>
          <li role="none">
            <a href="events/" role="menuitem">Events & Cities</a>
          </li>
          <li role="none">
            <a href="events/dallas-tx-2025-09-18-19/" role="menuitem">Tickets</a>
          </li>
          <li role="none">
            <a href="artists.html" role="menuitem">Artists</a>
          </li>
        </ul>
      </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content">
      <!-- Hero Section -->
      <section class="hero" aria-label="Dallas Hero">
        <div class="hero-content">
          <!-- Neon Sign -->
          <div class="neon-sign">
            <div class="neon-line line-1" data-text="CHOCOLATE">CHOCOLATE</div>
            <div class="neon-line line-2" data-text="AND&nbsp;ART">AND&nbsp;ART</div>
            <div class="neon-line line-3" data-text="SHOW">SHOW</div>
          </div>

          <!-- Buy Tickets CTA with Cialdini Copy -->
          <div class="hero-cta">
            <h2 class="cta-title">Dallas Tickets — 2 nights only (Thu–Fri). Join 10,000+ art lovers.</h2>
            <div class="ticket-buttons">
              <a class="btn-textured" href="/events/dallas-tx-2025-09-18-19/"
                aria-label="Get Dallas tickets - Thursday and Friday">
                Lock your spot — Thursday & Friday only, 21+
              </a>
              <a class="btn-textured" href="#subscribe" aria-label="Join insider list for early access">
                Join the Insider List — early‑bird drops + lineup first
              </a>
            </div>
          </div>
        </div>
      </section>

      <!-- RGB Big-Type Interstitial Wall -->
      <section class="rgb-wall" aria-label="Intermission banner">
        <div class="rgb-hero">
          <span class="rgb-word" data-txt="ART">ART</span>
          <span class="rgb-plus">+</span>
          <span class="rgb-word" data-txt="MUSIC">MUSIC</span>
          <span class="rgb-plus">+</span>
          <span class="rgb-word" data-txt="CHOCOLATE">CHOCOLATE</span>
          <span class="rgb-eq">=</span>
          <span class="rgb-word" data-txt="DALLAS">DALLAS</span>
        </div>
        <a class="btn-textured" href="/events/dallas-tx-2025-09-18-19/" rel="noopener noreferrer">
          BUY TICKETS
        </a>
      </section>

      <!-- About Section -->
      <section id="about" class="about-section">
        <div class="container">
          <h2>Experience the Art</h2>
          <p>Immersive art installations, live music performances, body painting, and artisan chocolate come together
            for two unforgettable nights in Dallas.</p>
        </div>
      </section>

      <!-- CTA Bar -->
      <section class="cta-bar">
        <div class="container">
          <div class="cta-grid">
            <div class="cta-item">
              <h3>Show Your Work (Artists)</h3>
              <p>Early submissions get priority placement.</p>
              <button class="btn-textured"
                onclick="openCompose('<EMAIL>', 'Artist Submission — Dallas/September', 'Links + portfolio + dimensions')">
                Submit your work — priority to early entries
              </button>
            </div>
            <div class="cta-item">
              <h3>Sell With Us (Vendors)</h3>
              <p>Average 3k+ attendees per night.</p>
              <button class="btn-textured"
                onclick="openCompose('<EMAIL>', 'Vendor Application — Dallas/September', 'Line sheet + power needs')">
                Apply to vend — avg. 3k+ attendees/night
              </button>
            </div>
            <div class="cta-item">
              <h3>Play Our Stage (Music)</h3>
              <p>Curated local lineup; limited slots.</p>
              <button class="btn-textured"
                onclick="openCompose('<EMAIL>', 'Music/DJ Submission — Dallas/September', 'Links + tech rider')">
                Pitch your set — curated local lineup (limited slots)
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Cities Grid -->
      <section class="cities-section">
        <div class="container">
          <h2>Events & Cities</h2>
          <div class="cities-grid">
            <div class="city-card">
              <h3>Dallas, TX</h3>
              <p>September 18-19, 2025</p>
              <p>Lofty Spaces</p>
              <a href="/events/dallas-tx-2025-09-18-19/" class="btn-textured">View Event</a>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer -->
    <footer class="site-footer" id="site-footer">
      <div class="footer-inner">
        <a href="index.html" class="brand-ft">
          <span>Chocolate & Art Show</span>
        </a>
        <nav class="footer-nav">
          <a href="index.html">Home</a>
          <a href="events/">Events</a>
          <a href="artists.html">Artists</a>
          <a href="vendors.html">Vendors</a>
          <a href="music.html">Music</a>
          <a href="faq.html">FAQ</a>
          <a href="contact.html">Contact</a>
        </nav>
        <nav class="footer-social">
          <a href="#" aria-label="Instagram">IG</a>
          <a href="#" aria-label="Facebook">FB</a>
          <a href="#" aria-label="Email">Email</a>
        </nav>
        <div class="footer-celebrate">
          <button class="btn-modern" id="footer-confetti-trigger" aria-label="Celebrate Dallas">
            <span class="btn-modern__inner">Celebrate Dallas</span>
          </button>
        </div>
      </div>
      <div class="footer-legal">
        <small>© 2025 Chocolate & Art Show · 21+ (ID required) · Lofty Spaces · Sept 18–19</small>
      </div>
    </footer>

    <canvas id="confetti-canvas" class="confetti-canvas" aria-hidden="true"></canvas>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/compose.js"></script>
    <script src="assets/js/confetti.js"></script>

  </body>

</html>