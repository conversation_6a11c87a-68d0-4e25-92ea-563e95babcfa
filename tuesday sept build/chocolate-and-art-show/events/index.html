<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Events & Cities — Chocolate & Art Show</title>
  <meta name="description" content="Chocolate & Art Show events across multiple cities. Immersive art, live music, and artisan chocolate experiences.">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="../assets/images/choco-logo.png">

  <style>
    :root {
      --pink: #ff2ebd;
      --pink-2: #ff7ad6;
      --white: #ffffff;
      --bg: #000;
      --card-bg: #1a1a1a;
      --card-bd: #333;
      --fg: #eef2ff;
      --name: #fff;
    }

    * { box-sizing: border-box; }
    html, body { height: 100%; margin: 0; padding: 0; }

    body {
      background: var(--bg);
      color: var(--white);
      font-family: ui-sans-serif, system-ui, -apple-system, "Segoe UI", Roboto, Inter, Arial, sans-serif;
      padding-top: 80px;
    }

    /* Navigation */
    .nav {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      background: rgba(0, 0, 0, 0.9);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .nav-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 2rem;
      max-width: 1200px;
      margin: 0 auto;
    }

    .nav-logo {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: var(--white);
      text-decoration: none;
      font-weight: 800;
    }

    .nav-menu {
      display: flex;
      list-style: none;
      margin: 0;
      padding: 0;
      gap: 2rem;
    }

    .nav-menu a {
      color: var(--white);
      text-decoration: none;
      font-weight: 600;
      transition: color 0.3s ease;
    }

    .nav-menu a:hover {
      color: var(--pink);
    }

    /* Container */
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    /* Header */
    .events-header {
      text-align: center;
      padding: 4rem 0 2rem;
    }

    .events-header h1 {
      font-size: clamp(2.5rem, 6vw, 4rem);
      margin-bottom: 1rem;
      color: var(--pink);
    }

    .events-header p {
      font-size: 1.2rem;
      color: var(--fg);
      max-width: 600px;
      margin: 0 auto;
    }

    /* Cities Grid */
    .cities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 2rem;
      margin: 3rem 0;
    }

    .city-card {
      background: var(--card-bg);
      border: 2px solid var(--card-bd);
      padding: 2rem;
      border-radius: 8px;
      text-align: center;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .city-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(255, 46, 189, 0.2);
    }

    .city-card h2 {
      font-size: 2rem;
      margin-bottom: 1rem;
      color: var(--pink);
    }

    .city-card .date {
      font-size: 1.2rem;
      margin-bottom: 0.5rem;
      color: var(--white);
      font-weight: 600;
    }

    .city-card .venue {
      font-size: 1rem;
      margin-bottom: 1.5rem;
      color: var(--fg);
    }

    .city-card .status {
      display: inline-block;
      padding: 0.5rem 1rem;
      background: var(--pink);
      color: var(--white);
      border-radius: 20px;
      font-size: 0.9rem;
      font-weight: 600;
      margin-bottom: 1.5rem;
    }

    .btn {
      display: inline-block;
      padding: 1rem 2rem;
      background: linear-gradient(135deg, var(--pink), var(--pink-2));
      color: var(--white);
      text-decoration: none;
      font-weight: 800;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      border: 2px solid transparent;
      transition: all 0.3s ease;
      border-radius: 4px;
    }

    .btn:hover {
      background: linear-gradient(135deg, var(--pink-2), var(--pink));
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(255, 46, 189, 0.3);
    }

    .btn-outline {
      background: transparent;
      border: 2px solid var(--card-bd);
      color: var(--fg);
    }

    .btn-outline:hover {
      border-color: var(--pink);
      color: var(--pink);
    }

    /* Coming Soon */
    .coming-soon {
      text-align: center;
      padding: 4rem 0;
      background: var(--card-bg);
      margin: 4rem 0;
      border-radius: 8px;
    }

    .coming-soon h2 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: var(--pink);
    }

    .coming-soon p {
      font-size: 1.2rem;
      color: var(--fg);
      margin-bottom: 2rem;
    }

    /* Footer */
    .site-footer {
      background: #0a0a0a;
      color: #eaeaea;
      padding: 24px 16px;
      margin-top: 4rem;
    }

    .footer-inner {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      flex-wrap: wrap;
      gap: 1rem;
    }

    .brand {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      color: inherit;
      text-decoration: none;
      font-weight: 600;
    }

    .footer-nav {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
    }

    .footer-icon {
      color: #cfcfcf;
      text-decoration: none;
      font-size: .95rem;
    }

    .footer-icon:hover {
      color: #fff;
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="nav" role="navigation" aria-label="Main navigation">
    <div class="nav-container">
      <a href="../index.html" class="nav-logo" aria-label="Chocolate & Art Show home">
        <img src="../assets/images/choco-logo.png" alt="Chocolate & Art Show logo" width="40" height="40">
        <span>Chocolate & Art</span>
      </a>

      <ul class="nav-menu" role="menubar">
        <li role="none"><a href="../index.html" role="menuitem">Home</a></li>
        <li role="none"><a href="../index.html#about" role="menuitem">About</a></li>
        <li role="none"><a href="index.html" role="menuitem" aria-current="page">Events & Cities</a></li>
        <li role="none"><a href="dallas-tx-2025-09-18-19/" role="menuitem">Tickets</a></li>
        <li role="none"><a href="../artists.html" role="menuitem">Artists</a></li>
      </ul>
    </div>
  </nav>

  <!-- Main Content -->
  <main id="main-content">
    <div class="container">
      <section class="events-header">
        <h1>Events & Cities</h1>
        <p>Immersive art experiences coming to cities across the country. Each event features local artists, musicians, and artisan chocolate.</p>
      </section>

      <div class="cities-grid">
        <!-- Dallas Event -->
        <div class="city-card">
          <h2>Dallas, TX</h2>
          <div class="date">September 18-19, 2025</div>
          <div class="venue">Lofty Spaces</div>
          <div class="status">Tickets Available</div>
          <p>Two nights of immersive art, live music, body painting, and artisan chocolate in the heart of Dallas.</p>
          <a href="dallas-tx-2025-09-18-19/" class="btn">View Event Details</a>
        </div>

        <!-- Future Cities -->
        <div class="city-card">
          <h2>Austin, TX</h2>
          <div class="date">Coming Soon</div>
          <div class="venue">Venue TBA</div>
          <div class="status">Announcing Soon</div>
          <p>The creative energy of Austin meets chocolate and art. Stay tuned for details.</p>
          <button class="btn btn-outline" onclick="openCompose('<EMAIL>', 'Austin Event Updates', 'Please notify me when Austin tickets become available.')">
            Get Notified
          </button>
        </div>

        <div class="city-card">
          <h2>Houston, TX</h2>
          <div class="date">Coming Soon</div>
          <div class="venue">Venue TBA</div>
          <div class="status">Announcing Soon</div>
          <p>Space City's vibrant arts scene gets a chocolate and art fusion experience.</p>
          <button class="btn btn-outline" onclick="openCompose('<EMAIL>', 'Houston Event Updates', 'Please notify me when Houston tickets become available.')">
            Get Notified
          </button>
        </div>
      </div>

      <!-- Coming Soon Section -->
      <section class="coming-soon">
        <h2>More Cities Coming Soon</h2>
        <p>We're expanding to new cities throughout 2025. Want us in your city?</p>
        <button class="btn" onclick="openCompose('<EMAIL>', 'City Request', 'I would like to see Chocolate & Art Show come to my city...')">
          Request Your City
        </button>
      </section>
    </div>
  </main>

  <!-- Footer -->
  <footer class="site-footer">
    <div class="footer-inner">
      <a class="brand" href="../index.html" aria-label="Chocolate & Art Show home">
        <img src="../assets/images/choco-logo.png" width="24" height="24" alt="C&A">
        <span>Chocolate & Art Show</span>
      </a>

      <nav class="footer-nav" aria-label="Social Links">
        <a class="footer-icon" href="#" aria-label="Instagram">IG</a>
        <a class="footer-icon" href="#" aria-label="Facebook">FB</a>
        <a class="footer-icon" href="#" aria-label="Email">Email</a>
      </nav>
    </div>
  </footer>

  <!-- JavaScript -->
  <script src="../assets/js/main.js"></script>
  <script src="../assets/js/compose.js"></script>

</body>
</html>
