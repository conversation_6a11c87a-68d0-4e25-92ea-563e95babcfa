/**
 * Confetti Animation - Emits toward center
 * Respects prefers-reduced-motion and performance constraints
 */
(function() {
  const trigger = document.getElementById("footer-confetti-trigger");
  const canvas = document.getElementById("confetti-canvas");
  
  if (!trigger || !canvas) return;
  
  // Check for reduced motion preference and performance constraints
  const prefersReducedMotion = window.matchMedia && window.matchMedia("(prefers-reduced-motion: reduce)").matches;
  const saveData = navigator.connection && navigator.connection.saveData === true;
  const lowMemory = navigator.deviceMemory && Number(navigator.deviceMemory) <= 2;
  
  if (prefersReducedMotion || saveData || lowMemory) return;
  
  const ctx = canvas.getContext("2d");
  
  function resizeCanvas() {
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;
  }
  
  resizeCanvas();
  window.addEventListener("resize", resizeCanvas);
  
  // Confetti particle constructor
  function Particle(x, y) {
    const angle = Math.PI / 2 + Math.random() * Math.PI;
    const velocity = 6 + Math.random() * 6;
    
    this.x = x;
    this.y = y;
    this.vx = Math.cos(angle) * velocity;
    this.vy = Math.sin(angle) * velocity - 4;
    this.size = 6 + Math.random() * 6;
    this.rotation = Math.random() * Math.PI;
    this.vr = (Math.random() - 0.5) * 0.2;
    this.color = ["#f8e16b", "#f6a6ff", "#8ad5ff", "#ff8a8a", "#9d7cff", "#ffffff"][Math.random() * 6 | 0];
    this.alpha = 1;
    this.shape = Math.random() < 0.5 ? "rect" : "tri";
  }
  
  Particle.prototype.update = function() {
    this.vy += 0.3; // gravity
    this.vx *= 0.99; // air resistance
    this.vy *= 0.99;
    this.x += this.vx;
    this.y += this.vy;
    this.rotation += this.vr;
    this.alpha *= 0.985; // fade out
  };
  
  Particle.prototype.draw = function() {
    ctx.globalAlpha = Math.max(this.alpha, 0);
    ctx.save();
    ctx.translate(this.x, this.y);
    ctx.rotate(this.rotation);
    ctx.fillStyle = this.color;
    
    if (this.shape === "rect") {
      ctx.fillRect(-this.size / 2, -this.size / 2, this.size, this.size * 0.7);
    } else {
      ctx.beginPath();
      ctx.moveTo(0, -this.size / 2);
      ctx.lineTo(this.size / 2, this.size / 2);
      ctx.lineTo(-this.size / 2, this.size / 2);
      ctx.closePath();
      ctx.fill();
    }
    
    ctx.restore();
  };
  
  function createConfetti(options) {
    const startTime = performance.now();
    const duration = options && options.duration || 900;
    const particles = [];
    const particleCount = options && options.particleCount || 160;
    const startX = options && options.x || canvas.width / 2;
    const startY = options && options.y || canvas.height - 80;
    
    // Create particles
    for (let i = 0; i < particleCount; i++) {
      particles.push(new Particle(startX, startY));
    }
    
    function animate(currentTime) {
      const elapsed = currentTime - startTime;
      
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      for (let i = 0; i < particles.length; i++) {
        particles[i].update();
        particles[i].draw();
      }
      
      if (elapsed < duration) {
        requestAnimationFrame(animate);
      } else {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
      }
    }
    
    requestAnimationFrame(animate);
  }
  
  // Event listener for confetti trigger
  trigger.addEventListener("click", function() {
    const rect = trigger.getBoundingClientRect();
    const x = rect.left + rect.width / 2;
    const y = rect.top + rect.height / 2;
    
    createConfetti({
      x: x,
      y: Math.min(y, window.innerHeight - 80),
      duration: 1000,
      particleCount: 180
    });
  });
})();
