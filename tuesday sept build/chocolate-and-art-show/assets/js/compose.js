/**
 * Gmail Compose Integration
 * Opens Gmail compose window with fallback to mailto
 */
function openCompose(to, subject, body) {
  const q = new URLSearchParams({
    view: 'cm',
    fs: '1',
    to: to,
    su: subject,
    body: body
  }).toString();
  
  const gmail = `https://mail.google.com/mail/?${q}`;
  const w = window.open(gmail, '_blank');
  
  // Fallback to mailto if Gmail doesn't open
  setTimeout(() => {
    if (!w || w.closed) {
      location.href = `mailto:${to}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    }
  }, 600);
}
