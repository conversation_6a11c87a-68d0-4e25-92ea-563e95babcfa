/* ===== Base Styles - Normalize + Typography ===== */

/* Modern CSS Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
}

html,
body {
  height: 100%;
}

body {
  line-height: var(--line-height-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
}

input,
button,
textarea,
select {
  font: inherit;
}

p,
h1,
h2,
h3,
h4,
h5,
h6 {
  overflow-wrap: break-word;
}

#root,
#__next {
  isolation: isolate;
}

/* Base Typography */
html {
  font-size: 16px;
}

body {
  background: var(--bg);
  color: var(--fg);
  font-family: var(--font-family-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
}

/* Headings */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--name);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--space-md);
}

h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-extrabold);
}

h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
}

h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
}

h4 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

h5 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
}

h6 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
}

/* Text Elements */
p {
  margin-bottom: var(--space-md);
}

a {
  color: var(--pink);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover,
a:focus-visible {
  color: var(--pink-2);
}

a:focus-visible {
  outline: 2px solid var(--pink);
  outline-offset: 2px;
}

/* Lists */
ul,
ol {
  margin-bottom: var(--space-md);
  padding-left: var(--space-lg);
}

li {
  margin-bottom: var(--space-xs);
}

/* Code */
code,
pre {
  font-family: var(--font-family-mono);
  font-size: var(--font-size-sm);
}

code {
  background: var(--card-bg);
  border: 1px solid var(--card-bd);
  border-radius: var(--radius-sm);
  padding: var(--space-xs) var(--space-sm);
}

pre {
  background: var(--card-bg);
  border: 1px solid var(--card-bd);
  border-radius: var(--radius-md);
  overflow-x: auto;
  padding: var(--space-md);
}

pre code {
  background: none;
  border: none;
  padding: 0;
}

/* Form Elements */
input,
textarea,
select {
  background: var(--card-bg);
  border: 1px solid var(--card-bd);
  border-radius: var(--radius-md);
  color: var(--fg);
  padding: var(--space-sm) var(--space-md);
  transition: border-color var(--transition-fast);
}

input:focus,
textarea:focus,
select:focus {
  border-color: var(--pink);
  outline: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--bg);
  color: var(--fg);
  padding: 8px;
  text-decoration: none;
  z-index: var(--z-tooltip);
  border-radius: var(--radius-md);
}

.skip-link:focus {
  top: 6px;
}

/* Focus management */
:focus-visible {
  outline: 2px solid var(--pink);
  outline-offset: 2px;
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  :root {
    --card-bd: #666;
  }
}

/* Print styles */
@media print {
  *,
  *::before,
  *::after {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a,
  a:visited {
    text-decoration: underline;
  }
  
  a[href]::after {
    content: " (" attr(href) ")";
  }
  
  abbr[title]::after {
    content: " (" attr(title) ")";
  }
  
  pre {
    white-space: pre-wrap !important;
  }
  
  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }
  
  thead {
    display: table-header-group;
  }
  
  tr,
  img {
    page-break-inside: avoid;
  }
  
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  
  h2,
  h3 {
    page-break-after: avoid;
  }
}
