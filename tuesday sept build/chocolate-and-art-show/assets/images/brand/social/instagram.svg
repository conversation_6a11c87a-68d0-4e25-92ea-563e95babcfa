<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <radialGradient id="grad1" cx="50" cy="50" r="50" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#f58529"/>
      <stop offset="50%" stop-color="#dd2a7b"/>
      <stop offset="100%" stop-color="#515bd4"/>
    </radialGradient>
  </defs>
  <rect x="0" y="0" width="100" height="100" rx="20" fill="url(#grad1)"/>
  <g opacity="0.2" stroke="#fff" stroke-width="1">
    <line x1="20" y1="80" x2="80" y2="20"/>
    <line x1="30" y1="80" x2="80" y2="30"/>
    <line x1="20" y1="70" x2="70" y2="20"/>
    <circle cx="50" cy="25" r="2" fill="#fff"/>
    <circle cx="25" cy="50" r="2" fill="#fff"/>
    <circle cx="70" cy="50" r="2" fill="#fff"/>
    <circle cx="50" cy="75" r="2" fill="#fff"/>
  </g>
  <rect x="28" y="28" width="44" height="44" rx="12" fill="none" stroke="#fff" stroke-width="5"/>
  <circle cx="50" cy="50" r="12" fill="none" stroke="#fff" stroke-width="5"/>
  <circle cx="68" cy="32" r="4" fill="#fff"/>
</svg>