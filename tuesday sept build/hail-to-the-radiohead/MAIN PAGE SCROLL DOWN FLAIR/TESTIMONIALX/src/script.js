let card = document.querySelectorAll(".card");
let contentBox = document.querySelectorAll(".contentBox");

for (let i = 0; i < card.length; i++) {
  card[i].addEventListener("mouseover", function () {
    for (let i = 0; i < contentBox.length; i++) {
      contentBox[i].className = "contentBox";
    }
    document.getElementById(this.dataset.id).className = "contentBox active";

    for (let i = 0; i < card.length; i++) {
      card[i].className = "card";
    }
    this.className = "card active";
  });
}

/*inspiration
https://dribbble.com/shots/10464678-Daily-UI-039-Testimonials
*/
