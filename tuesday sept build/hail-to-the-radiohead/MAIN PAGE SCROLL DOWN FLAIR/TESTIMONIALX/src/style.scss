@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;700&display=swap");

@mixin mQ($rem) {
  @media screen and (min-width: $rem) {
    @content;
  }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  display: grid;
  place-items: start;
  min-height: 100vh;
  min-height: 100svh;
  font-family: "Poppins", sans-serif;
  color: #26273e;
  padding-block: min(20vh, 3rem);

  @include mQ(61.25rem) {
    place-items: center;
  }
}

section {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  width: calc(min(90rem, 85%));
  margin: 0 auto;

  @include mQ(61.25rem) {
    column-gap: 5rem;
  }

  h1 {
    grid-column: span 12;
    text-transform: capitalize;
    font-size: 2.4rem;
    margin-bottom: 2rem;
    font-weight: 700;

    @include mQ(61.25rem) {
      font-size: 4rem;
      margin-bottom: 4rem;
    }
  }

  .cards {
    grid-column: span 12;
    display: grid;
    gap: 2rem;

    @include mQ(61.25rem) {
      grid-column: span 5;
    }

    .card {
      cursor: pointer;
      padding: 2em;
      border-radius: 1rem;
      display: grid;
      grid-template-columns: auto 1fr;
      column-gap: 2.188rem;
      align-items: center;
      transition: 0.3s;
      position: relative;
      border: 0.094rem solid transparent;

      img {
        display: block;
        width: 3.35rem;
        height: 3.35rem;
        border-radius: 50%;
        filter: grayscale(1);
        transition: 0.5s;
      }

      > div {
        h3 {
          text-transform: capitalize;
          font-size: 1.025rem;
        }

        p {
          text-transform: capitalize;
          color: #767676;
          font-size: 0.9rem;
        }
      }
    }
    .card.active {
      background: #fff;
      border: 0.094rem solid #0f172a14;

      .gradient {
        background-image: linear-gradient(
          to right,
          #4755690a,
          #9d0cb28a,
          #4343c899,
          #4755690a
        );
        width: 50%;
        height: 0.094rem;
        position: absolute;
        content: "";
        bottom: -0.063rem;
        left: 50%;
        transform: translateX(-50%);
        box-shadow: 0px 0.125rem 0.75rem #4343c84d;
      }
    }
    .card.active img {
      filter: grayscale(0);
    }
  }

  .content {
    grid-column: span 12;
    position: relative;
    width: 100%;
    overflow: inherit;
    margin-top: 2rem;

    @include mQ(61.25rem) {
      grid-column: span 7;
      margin-top: 0;
      height: auto;
    }

    .contentBox {
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: start;

      @include mQ(61.25rem) {
        align-items: center;
      }

      .text {
        padding-bottom: 2rem;

        @include mQ(61.25rem) {
          padding-bottom: 0;
        }
      }

      h2 {
        transition: 0.5s;
        opacity: 0;
      }

      p {
        transition: 0.5s;
        opacity: 0;
        margin-top: 1.25rem;
      }

      span {
        display: inline-block;
        transition: 0.5s;
        opacity: 0;
        margin-top: 0.625rem;

        svg {
          width: 1.25rem;
          color: #eca633;
        }
      }
    }

    .contentBox.active h2 {
      opacity: 1;
      transition-delay: 0.5s;
    }
    .contentBox.active span {
      opacity: 1;
      transition-delay: 0.7s;
    }
    .contentBox.active p {
      opacity: 1;
      transition-delay: 0.9s;
    }
  }
}

body::-webkit-scrollbar {
  width: 0.8em;
}

body::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0.375rem rgba(0, 0, 0, 0.3);
}

body::-webkit-scrollbar-thumb {
  background-color: #3f3f3f;
}
