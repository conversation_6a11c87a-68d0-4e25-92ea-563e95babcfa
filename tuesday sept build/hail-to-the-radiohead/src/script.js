const words = [
  "Fear", "Control", "Truth", "Lies", "Hate", "Trust", "Corrupt", "Power", "Money", 
  "Faith", "Justice", "Chaos", "Dream", "Liberty", "War", "Peace", "Deceive", "Hope", 
  "Terror", "Love", "Future", "Voice", "Change", "Revolt", "Silence", "Freedom", 
  "Greed", "Rise", "Fall", "Believe", "Unite", "Break", "Build", "Fake", "Real", 
  "Anger", "Joy", "Dark", "Light", "Noise", "Quiet", "Strength", "Weakness", "Control", 
  "Escape", "Lost", "Found", "Open", "Close", "Win", "Lose", "Fight", "Submit", 
  "Rule", "Anarchy", "Free", "Bound", "Peaceful", "Violent", "Leader", "Follower", 
  "Empty", "Full", "Victory", "Defeat", "Faithful", "Faithless", "Known", "Unknown", 
  "Safe", "Danger", "Silent", "Loud", "Order", "Disorder", "Alive", "Dead", 
  "Vision", "<PERSON>", "<PERSON>", "<PERSON>", "Rise", "Decay", "Heal", "Wound", "<PERSON>ubt", 
  "Certainty", "Visible", "Hidden", "Strong", "Fragile", "Open", "Close", "Present", 
  "Absent", "Connected", "Detached"
];

const colors = ['#D0001D', '#0D5436', '#093588', '#FDA223', '#F8551A', '#101624', '#EAEFF0']

function getRandomColorPair() {
  const bgIndex = Math.floor(Math.random() * colors.length);
  let cIndex;
  do {
    cIndex = Math.floor(Math.random() * colors.length);
  } while (cIndex === bgIndex);
  return { bg: colors[bgIndex], c: colors[cIndex] };
}

const canvas = document.querySelector('ul');
canvas.innerHTML = words.map(word => {
  const { bg, c } = getRandomColorPair();
  return `<li style="--bg: ${bg};--c: ${c};">${word}</li>`;
}).join('');