@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;700&display=swap");
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  display: grid;
  place-items: start;
  min-height: 100vh;
  min-height: 100svh;
  font-family: "Poppins", sans-serif;
  color: #26273e;
  padding-block: min(20vh, 3rem);
}
@media screen and (min-width: 61.25rem) {
  body {
    place-items: center;
  }
}

section {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  width: calc(min(90rem, 85%));
  margin: 0 auto;
}
@media screen and (min-width: 61.25rem) {
  section {
    column-gap: 5rem;
  }
}
section h1 {
  grid-column: span 12;
  text-transform: capitalize;
  font-size: 2.4rem;
  margin-bottom: 2rem;
  font-weight: 700;
}
@media screen and (min-width: 61.25rem) {
  section h1 {
    font-size: 4rem;
    margin-bottom: 4rem;
  }
}
section .cards {
  grid-column: span 12;
  display: grid;
  gap: 2rem;
}
@media screen and (min-width: 61.25rem) {
  section .cards {
    grid-column: span 5;
  }
}
section .cards .card {
  cursor: pointer;
  padding: 2em;
  border-radius: 1rem;
  display: grid;
  grid-template-columns: auto 1fr;
  column-gap: 2.188rem;
  align-items: center;
  transition: 0.3s;
  position: relative;
  border: 0.094rem solid transparent;
}
section .cards .card img {
  display: block;
  width: 3.35rem;
  height: 3.35rem;
  border-radius: 50%;
  filter: grayscale(1);
  transition: 0.5s;
}
section .cards .card > div h3 {
  text-transform: capitalize;
  font-size: 1.025rem;
}
section .cards .card > div p {
  text-transform: capitalize;
  color: #767676;
  font-size: 0.9rem;
}
section .cards .card.active {
  background: #fff;
  border: 0.094rem solid #0f172a14;
}
section .cards .card.active .gradient {
  background-image: linear-gradient(to right, #4755690a, #9d0cb28a, #4343c899, #4755690a);
  width: 50%;
  height: 0.094rem;
  position: absolute;
  content: "";
  bottom: -0.063rem;
  left: 50%;
  transform: translateX(-50%);
  box-shadow: 0px 0.125rem 0.75rem #4343c84d;
}
section .cards .card.active img {
  filter: grayscale(0);
}
section .content {
  grid-column: span 12;
  position: relative;
  width: 100%;
  overflow: inherit;
  margin-top: 2rem;
}
@media screen and (min-width: 61.25rem) {
  section .content {
    grid-column: span 7;
    margin-top: 0;
    height: auto;
  }
}
section .content .contentBox {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: start;
}
@media screen and (min-width: 61.25rem) {
  section .content .contentBox {
    align-items: center;
  }
}
section .content .contentBox .text {
  padding-bottom: 2rem;
}
@media screen and (min-width: 61.25rem) {
  section .content .contentBox .text {
    padding-bottom: 0;
  }
}
section .content .contentBox h2 {
  transition: 0.5s;
  opacity: 0;
}
section .content .contentBox p {
  transition: 0.5s;
  opacity: 0;
  margin-top: 1.25rem;
}
section .content .contentBox span {
  display: inline-block;
  transition: 0.5s;
  opacity: 0;
  margin-top: 0.625rem;
}
section .content .contentBox span svg {
  width: 1.25rem;
  color: #eca633;
}
section .content .contentBox.active h2 {
  opacity: 1;
  transition-delay: 0.5s;
}
section .content .contentBox.active span {
  opacity: 1;
  transition-delay: 0.7s;
}
section .content .contentBox.active p {
  opacity: 1;
  transition-delay: 0.9s;
}

body::-webkit-scrollbar {
  width: 0.8em;
}

body::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0.375rem rgba(0, 0, 0, 0.3);
}

body::-webkit-scrollbar-thumb {
  background-color: #3f3f3f;
}