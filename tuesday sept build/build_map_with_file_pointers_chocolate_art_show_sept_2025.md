# Build Map with File Pointers — Chocolate & Art Show (Sept 2025)

> Inputs inspected: **/mnt/data/mostof.zip** (15 files)
> Goal: Wire the latest working landing + footer + contact + confetti + CTA behaviors into the **final static build**, preserving our Cialdini‑framed CTAs and SEO schemas. This is an **instructions‑only** map with explicit file pointers.

---

## A) Inventory from `mostof.zip` (what each file contains)

```
[HTML prototypes]
- chocolate_art_fixed.html
  • Full landing prototype w/ neon hero, video bg (#chocoArtVideo), nav, CTAs, cards.
  • Hard-coded Eventbrite URL present (Dallas single link).

- chocolate_art_transition_wall_footer.html
  • Fixed header + RGB big-type "wall" interstitial + modern footer shell.
  • Includes confetti trigger/button + canvas IDs (not the minified JS itself).

- transition_wall_footer_tickets_left_final_snippet.html
  • Same as above + a "Tickets Left" micro-banner style.
  • Good source for **final footer markup** and the **RGB wall section**.

- footer_confetti_opposite_direction_merged_0822_d.html
  • Footer markup + **minified confetti JS** that emits **toward center**.
  • Key IDs: `#site-footer`, `#footer-confetti-trigger`, `#confetti-canvas`.

- contact_page_v_2_icons_footer_thicker_cards.html
- contact_page_v_2_icons_footer_thicker_cards (1).html (dup)
- LATESTFRIDAYcontact_page.html (older)
  • Contact page prototypes; the **v_2** file is the final look (thicker card borders, icon grid, footer included).

- chocolatesimmersive.html
  • Immersive promo card (Bebas Neue) – optional "Experience" block or social banner. Not a full page.

[Markdown notes]
- ChatGPT-File update instructions.md
- c_a_implementation_plan_cards_nav_snap_testimonials_interlinking.md
- chocolate_art_flow_gateway_gallery_fix_list_0822_b.md
  • Implementation deltas, color tokens, scroll-snap/gallery notes.

[SVG assets]
- email logo in NEON.svg
- eventbrite logo in NEON.svg
- facebook logo NEON.svg
- INSTAGRAM.svg
```

---

## B) Final Project Tree (with **source → destination** pointers)

> Left side = **destination** in final repo · Right side comments = **which prototype/asset to pull from** (and what to strip/keep).

```
chocolate-and-art-show/
├─ index.html                                ← Base = chocolate_art_fixed.html (strip video; keep hero copy & CTAs)
│  # Remove external Google Fonts; use system stack; keep neon styles as CSS vars.
│  # Replace hardcoded Eventbrite with city-deep-links.
│  # Insert "RGB wall" interstitial below hero from transition_wall_footer_tickets_left_final_snippet.html.
│  # Insert CTA Bar (compose.js) between About and Cities (see Part C).
│
├─ contact.html                              ← Use contact_page_v_2_icons_footer_thicker_cards.html (latest v_2)
│  # Keep thicker card borders and icon footer; remove any Formspree endpoints.
│  # Update contact CTAs to Gmail compose (artists/vendors/music/press).
│
├─ events/
│  ├─ index.html                             ← New (cards list; simple grid)
│  └─ dallas-tx-2025-09-18-19/
│     ├─ index.html                          ← New (city detail w/ house rules + two ticket buttons)
│     └─ event-data.json                     ← New (hydrated into JSON-LD)
│
├─ artists.html                              ← New (simple apply page; compose buttons)
├─ vendors.html                              ← New (apply page; compose buttons)
├─ music.html                                ← New (apply page; compose buttons)
├─ faq.html                                   ← New (match FAQ schema Q&A)
│
├─ partials/
│  ├─ header.html                            ← From chocolate_art_fixed.html (extract <header> markup)
│  ├─ footer.html                            ← From transition_wall_footer_tickets_left_final_snippet.html (preferred)
│  └─ cta-bar.html                           ← New (Cialdini-framed trio of buttons; see Part C)
│
├─ assets/
│  ├─ css/
│  │  ├─ tokens.css                           ← Derive from MD notes (brand tokens)
│  │  ├─ base.css                             ← Normalize + type scale (system fonts)
│  │  ├─ layout.css                           ← Sections, containers, snap points
│  │  ├─ components.css                       ← Cards (straight edges, 2px border), nav, footer
│  │  ├─ buttons.css                          ← CTA + social (jh3y-inspired, straight lines)
│  │  ├─ gallery.css                          ← New (gateway lightbox)
│  │  └─ confetti.css                         ← Small canvas sizing if needed
│  ├─ js/
│  │  ├─ main.js                              ← From chocolate_art_fixed.html: nav active, smooth scroll (Lenis optional)
│  │  ├─ gallery.js                           ← New (6-image lightbox; tap-anywhere→next; ESC; closes after last)
│  │  ├─ compose.js                           ← New (Gmail compose + mailto fallback)
│  │  ├─ confetti.js                          ← Extract from footer_confetti_opposite_direction_merged_0822_d.html <script>
│  │  └─ dropdown.js                          ← If using Events & Cities dropdown (optional)
│  ├─ images/
│  │  ├─ brand/
│  │  │  ├─ choco-logo.png                    ← (already referenced in prototypes)
│  │  │  ├─ social/
│  │  │  │  ├─ instagram.svg                  ← from INSTAGRAM.svg (rename → instagram.svg)
│  │  │  │  ├─ facebook.svg                   ← from facebook logo NEON.svg (rename → facebook.svg)
│  │  │  │  ├─ email.svg                      ← from email logo in NEON.svg (rename → email.svg)
│  │  │  │  └─ eventbrite.svg                 ← from eventbrite logo in NEON.svg (rename → eventbrite.svg)
│  │  ├─ gallery/01.jpg ... 06.jpg            ← Provide/curate (not in zip); placeholders OK
│  │  └─ maps/dallas-lofty-spaces-static.jpg  ← Static map screenshot
│  └─ fonts/                                   ← (Optional local fonts; otherwise system UI stack)
│
├─ schema/
│  ├─ organization.json                       ← New (Org)
│  ├─ website.json                            ← New (WebSite)
│  ├─ faq.json                                ← New (FAQPage)
│  └─ events/dallas-2025-09-18.json           ← New (Event)
│
├─ sitemap.xml                                ← New
├─ robots.txt                                 ← New
└─ .well-known/security.txt                   ← New
```

---

## C) Section‑by‑Section Wiring (exact pointers & actions)

### 1) Header/Nav
- **Source**: `<header>` from **chocolate_art_fixed.html**.
- **Destination**: `partials/header.html` then included into `index.html` (static include or copy/paste).
- **Edits**:
  - Remove external Google Fonts `<link>`s.
  - Convert any class‑based colors to CSS variables from `assets/css/tokens.css`.
  - Ensure menu: `Home | About | Events & Cities ▼ | Tickets | Artists`.

### 2) Hero (No video)
- **Source**: **chocolate_art_fixed.html** hero markup/styles.
- **Action**: Delete the `<video id="chocoArtVideo">` block. Keep neon headline & CTA container.
- **CTAs (Cialdini)**:
  - Primary: **“Dallas Tickets — 2 nights only (Thu–Fri). Join 10,000+ art lovers.”** → `/events/dallas-tx-2025-09-18-19/`
  - Secondary: **“Join the Insider List — early‑bird drops + lineup first.”** → `#subscribe`

### 3) Interstitial “RGB Wall”
- **Source**: **transition_wall_footer_tickets_left_final_snippet.html** (the RGB big‑type wall section).
- **Destination**: Insert below hero in `index.html`.
- **Note**: Keep JS‑free; use CSS scroll‑snap if desired per `c_a_implementation_plan...md`.

### 4) CTA Bar (Apply / Pitch / Book)
- **Source**: New partial (`partials/cta-bar.html`).
- **Destination**: Between About and Cities on `index.html` + duplicate on `artists.html`, `vendors.html`, `music.html`.
- **Buttons & Actions (compose.js)**:
  - **Show Your Work (Artists)** — “Early submissions get priority placement.” → `openCompose('<EMAIL>', 'Artist Submission — City/Month', 'Links + portfolio + dimensions')`
  - **Sell With Us (Vendors)** — “Average 3k+ attendees per night.” → `openCompose('<EMAIL>', 'Vendor Application — City/Month', 'Line sheet + power needs')`
  - **Play Our Stage (Music)** — “Curated local lineup; limited slots.” → `openCompose('<EMAIL>', 'Music/DJ Submission — City/Month', 'Links + tech rider')`

### 5) Cities Grid
- **Source**: Build simple `.card` grid (straight edges, 2px border) — styles come from **contact_page_v_2_icons_footer_thicker_cards.html** (copy card style tokens).
- **Destination**: `events/index.html`.

### 6) Footer + Confetti (toward center)
- **Footer Markup Source**: **transition_wall_footer_tickets_left_final_snippet.html** or **chocolate_art_transition_wall_footer.html** (choose one; the "Tickets Left" version preferred).
- **Confetti JS Source**: **footer_confetti_opposite_direction_merged_0822_d.html** `<script>` block.
- **Destination**:
  - Paste footer HTML into `partials/footer.html`.
  - Extract `<script>` contents into `assets/js/confetti.js`; keep IDs `#footer-confetti-trigger` and `#confetti-canvas` intact.
  - Add `prefers-reduced-motion` check (present in minified bundle) — leave as is.

### 7) Contact Page
- **Source**: **contact_page_v_2_icons_footer_thicker_cards.html** (use this, ignore the older duplicate and LATESTFRIDAY variant).
- **Edits**: Remove any external fonts; swap footer block to partial include; replace any form actions with **compose.js** buttons.

### 8) Optional “Experience” Card
- **Source**: **chocolatesimmersive.html** (promo card).
- **Destination**: Place near "Experience the Art" section or use as social banner asset.

### 9) Gallery Lightbox (6 images)
- **Source**: Not in zip; implement per previous spec (tap anywhere→next; closes after 6 or `X`; ESC closes; focus trap).
- **Destination**: `assets/js/gallery.js` + `assets/css/gallery.css` + images under `assets/images/gallery/01..06.jpg`.

### 10) Social Icons
- **Source assets**: SVGs in the zip.
- **Destination**: `assets/images/brand/social/` with **renames**:
  - `INSTAGRAM.svg` → `instagram.svg`
  - `facebook logo NEON.svg` → `facebook.svg`
  - `email logo in NEON.svg` → `email.svg`
  - `eventbrite logo in NEON.svg` → `eventbrite.svg`
- **Usage**: Reference in footer & CTA buttons; keep monochrome for cohesion.

---

## D) Schemas & Metadata (drop‑in locations)

- **index.html** `<head>`: `<title>`, `<meta description>`, OpenGraph/Twitter tags, preload first gallery image, inline `schema/organization.json` and `schema/website.json`.
- **faq.html**: Inline `schema/faq.json` (Q&A nodes).
- **events/dallas-.../index.html**: Inline `schema/events/dallas-2025-09-18.json`.
- **Breadcrumb**: Optional single‑item breadcrumb on home; multi‑item on city pages.

---

## E) Cialdini‑Framed CTA Copy (approved)

- **Hero primary** (Scarcity + Commitment + Social Proof):
  **“Dallas Tickets — 2 nights only (Thu–Fri). Join 10,000+ art lovers.”**
- **Hero secondary** (Reciprocity):
  **“Join the Insider List — early‑bird drops + lineup first.”**
- **Artists** (Authority + Commitment):
  **“Submit your work — priority to early entries.”**
- **Vendors** (Social Proof + Reciprocity):
  **“Apply to vend — avg. 3k+ attendees/night.”**
- **Music** (Authority + Commitment):
  **“Pitch your set — curated local lineup (limited slots).”**
- **Tickets** (Urgency + Scarcity):
  **“Lock your spot — Thursday & Friday only, 21+.”**

---

## F) Exact TODO Order (no surprises)

1) **Create tree & folders** per Part B.
2) **Header/Hero**: import from `chocolate_art_fixed.html`; **remove video**; wire CTAs.
3) **RGB Wall**: insert interstitial section from `transition_wall_footer_tickets_left_final_snippet.html`.
4) **CTA Bar**: add `partials/cta-bar.html` + `assets/js/compose.js` (use snippet below).
5) **Footer**: import footer markup; port confetti JS to `assets/js/confetti.js` from `footer_confetti_opposite_direction_merged_0822_d.html`.
6) **Contact**: base from `contact_page_v_2_icons_footer_thicker_cards.html` → `contact.html`.
7) **Events**: scaffold city page + JSON-LD; update tickets URLs (Thu/Fri).
8) **Gallery**: implement 6‑image lightbox; connect to “Experience the Art” area.
9) **Icons**: copy/rename SVGs into `assets/images/brand/social/`.
10) **SEO**: add JSON-LD files; inline per page; update sitemap/robots.

**Compose snippet** (`assets/js/compose.js`):
```js
function openCompose(to, subject, body){
  const q = new URLSearchParams({view:'cm',fs:'1',to, su:subject, body}).toString();
  const gmail = `https://mail.google.com/mail/?${q}`;
  const w = window.open(gmail, '_blank');
  setTimeout(()=>{
    if(!w || w.closed) location.href = `mailto:${to}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
  }, 600);
}
```

---

## G) QA Checklist (ship block)
- [ ] No external fonts; system stack only; CSS vars in `tokens.css`.
- [ ] Header/nav links correct; Events dropdown (if used) keyboard‑friendly.
- [ ] Hero has **no video**; CTAs use approved Cialdini copy.
- [ ] RGB wall renders; no layout shift.
- [ ] CTA Bar buttons trigger Gmail compose; mobile fallback works.
- [ ] Footer confetti emits **toward center**; respects `prefers-reduced-motion`.
- [ ] Contact has thicker card borders; icons display; no 3rd‑party form posts.
- [ ] Events city page exposes Thu/Fri ticket links; **21+** copy consistent.
- [ ] JSON-LD validates (Org, WebSite, Event, FAQ); sitemap/robots present.
```

