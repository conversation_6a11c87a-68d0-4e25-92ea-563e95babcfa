# Chocolate & Art Show — Wire‑Tree (updated) + Flow of Assets + Fix List

> Notes captured from latest files and directives: **hidden SVG game in footer** (already linked), **new intro**, **first page placement (double Buy Tickets)**, **Gateway Gallery (fullscreen)**, **Artists gateway (5 featured, one per screen)**. Keep all existing pages; **replace landing only**.

---

## 1) Current vs Expected — compact wire‑tree (delta view)

```
RECOVERED PAGES (keep)
├─ /tickets                → tickets.html
├─ /schedule/dallas        → schedule.html
├─ /gallery/dallas         → gallery.html
├─ /faq/dallas             → faq.html
├─ /contact                → contact.html
├─ /artists                → artists/index.html
│  ├─ /artists/danica      → artists/danica.html
│  └─ /artists/david-v     → artists/david-v.html
└─ /play/kusama            → play/kusama/index.html   (interactive piece)

EXPECTED TREE (markdown) — MISSING now
├─ /about
├─ /artists/apply, /artists/faq, /artists/featured
├─ /blog
├─ /dallas (city hub)
├─ /faq/policies, /faq/photo-policy
├─ /gallery/hashtag
├─ /music/apply
├─ /parking (or /parking/dallas)
├─ /press, /resources, /sponsor, /volunteer, /sms
├─ /tickets/dallas, /tickets/policy, /thank-you
└─ /vendors, /vendors/apply, /vendors/faq
```

---

## 2) New Landing (/): structure & correct placements (replace only /)

```
/ (index.html)
├─ <header class="site-nav"> Home | About | Events & Cities ▼ | Tickets | Artists | Gallery | Contact |  [Buy Dallas Tickets]  ← sticky CTA (right)
├─ <main class="home-landing">   ← scoped styles/scripts (home.css, home.js)
│  ├─ Intro box (neon title)
│  │   • source: layered‑spin neon (uses /assets/morado.png) in **LITE** mode by default
│  │   • performance toggle → “Boost Glow” (progressive enhancement)
│  ├─ Hero CTA block (correct order)
│  │   • H2: “Buy Tickets Dallas”
│  │   • Two buttons side‑by‑side: [Thursday] [Friday]  (Eventbrite, same link w/ day text)
│  │   • 21+ badge + venue/dates (Lofty Spaces • Sept 18–19)
│  ├─ Interstitial RGB word wall (ART + MUSIC + CHOCOLATE = DALLAS)
│  │   • Secondary CTA: [Buy Tickets]
│  ├─ Gateway Gallery (fullscreen slider)  ← NEW (see §3)
│  ├─ “What to Expect” 3‑up strip → Artists / Gallery / FAQ
│  └─ Footer (brand, links, **hidden SVG game link**)  (see §5)
└─ <script defer> home.js (GSAP/Swiper only on /)
```

**Placement correction:** one **sticky** CTA in the header (Buy Dallas Tickets), and the two **primary** CTAs inside the hero (“Thursday” + “Friday”). This keeps the double‑CTA correct but **in the hero**, not the nav.

---

## 3) Gateway Gallery (fullscreen) — strip to essentials

**Goal:** A new fullscreen gallery gateway page/section that feels immersive, then routes to our existing `/gallery/dallas`.

```
Route: /gateway/gallery   (file: gateway-gallery.html)
Uses: GSAP + Swiper (from landing ZIP), kept minimal.
Assets: /assets/gallery/teaser/*  (reuse existing)

Structure (fullscreen):
├─ <section class="gateway-gallery fullscreen">  (100vh, black bg)
│  ├─ Swiper wrapper (no thumbs, no text; arrows + pagination only)
│  └─ Fixed bottom bar: [Enter Full Gallery] → /gallery/dallas
└─ Keyboard, wheel, touch enabled; respects prefers-reduced-motion.
```

**On Landing:** the “Gateway Gallery” can live as an inline 100vh section on `/` (first after hero), **or** as a separate page at `/gateway/gallery` if we want a clean URL. Either way, it launches to real gallery via the button.

---

## 4) Artists Gateway (5 featured, one per screen)

**Goal:** Spotlight artists in a big way. One artist per viewport with snap‑scroll.

```
Route: /artists/featured    (file: artists-featured.html)
Source: reuse gateway pattern; vertical snap (CSS scroll-snap) or Swiper vertical.
Count: 5 panels (curated for Dallas); each panel links to the artist detail.

Panels (initial set)
1) Danica       → /artists/danica.html   (existing)
2) David V      → /artists/david-v.html  (existing)
3) TBD‑3        → /artists/tbd‑3.html    (stub now)
4) TBD‑4        → /artists/tbd‑4.html    (stub now)
5) TBD‑5        → /artists/tbd‑5.html    (stub now)

Each panel: fullscreen image/video, name/bio snippet, [View Work] CTA → detail page.
```

**On Landing:** include a “Featured Artists” tile that deep‑links to `/artists/featured`.

---

## 5) Footer: hidden SVG game (already linked) — keep + tidy

```
Footer block
├─ Social (glitter icons): Facebook, Instagram, Eventbrite, Email
├─ Hidden SVG/Canvas link (easter egg):  ➜ /play/kusama/
│  • Keep link in the social block but visually subtle (icon without label)
│  • Add aria‑label="Interactive art"
│  • Keep off‑screen description for screen readers (sr‑only)
└─ Policy pins: 21+ event, hours, quick links
```

Implementation detail: keep the interactive piece **off the render path** (no script execution on load); navigate to `/play/kusama/` on click so it doesn’t hurt LCP.

---

## 6) Flow of assets (names + where they plug in)

```
/ (landing)
├─ CSS  → /assets/css/home.css        (scoped: .home-landing …)
├─ JS   → /assets/js/home.js          (GSAP, Swiper; only on /)
├─ Intro layered spin uses → /assets/morado.png
├─ Video hero poster       → /assets/images/hero-chocolate-art-tonight.png
├─ Video hero source       → /assets/videos/chocoartshow.mp4 (autoplay muted loop)
├─ RGB wall                → inline CSS only (no deps)
├─ Gallery teasers         → /assets/gallery/teaser/1.jpg … 6.jpg
└─ Footer icons            → /assets/icons/facebook.svg, instagram.svg, eventbrite.svg, minutemailer.svg

/gateway/gallery
├─ CSS/JS: reuse home.css/home.js subset (or gateway‑gallery.css/js if we split)
└─ Images: same teasers (or a curated subset), full‑screen Swiper

/artists/featured
├─ CSS: artists‑gateway.css (scroll‑snap or Swiper vertical)
└─ Assets: per‑artist hero image/video; links to existing detail pages

/play/kusama
└─ interactive_homage_to_kusama_preview.html (kept standalone)
```

---

## 7) Strip‑down rules (to keep it fast & clean)

- **Scope styles** to `.home-landing`, `.gateway-gallery`, `.artists-gateway` to avoid bleed.
- **Load GSAP/Swiper only where used** (no global includes).
- **Prefer laziness:** `loading="lazy"` for non‑hero images; defer scripts; no heavy webfonts.
- **Reduced motion:** respect `prefers-reduced-motion`; default intro in **LITE** mode; button toggles full glow.
- **Accessibility:** aria‑labels on all CTAs; contrast ≥ 4.5:1; keyboard focus visible.

---

## 8) What we have vs what’s left (actionable list)

**Have now (wired & named)**
```
CORE PAGES
✓ /tickets, /schedule/dallas, /gallery/dallas, /faq/dallas, /contact, /artists
✓ /artists/danica, /artists/david-v
✓ /play/kusama (hidden link kept in footer)

LANDING ASSETS
✓ Neon intro (layered‑spin LITE) using /assets/morado.png
✓ Hero with two CTAs (Thu/Fri) — needs placement correction (see §2)
✓ RGB interstitial wall
✓ Gallery teaser grid (teasers 1–6)
✓ Footer social (glitter icons) + hidden interactive link
```

**Left to fix / build**
```
IMMEDIATE (for this drop)
1) Swap in new landing shell (home.css/home.js) and correct CTA placements.
2) Add Gateway Gallery (fullscreen) on / or at /gateway/gallery (button to /gallery/dallas).
3) Add Artists Featured (5 screens) at /artists/featured (stubs for 3–5).
4) Ensure footer’s hidden game link points to /play/kusama and is non‑blocking for performance.

NEAR‑TERM (from sitemap)
5) /about, /dallas (city hub), /tickets/dallas, /tickets/policy, /thank-you
6) /faq/policies, /faq/photo-policy
7) /vendors, /vendors/apply, /vendors/faq, /music/apply
8) /parking (Dallas), /press, /resources, /sponsor, /volunteer, /sms, /blog
9) /gallery/hashtag (aggregated UGC)
```

---

## 9) Milestone handoff (what to deliver now)

- `index.html` (landing) — scoped to `.home-landing`, header sticky CTA + hero double buttons.
- `gateway-gallery.html` — fullscreen Swiper with “Enter Full Gallery” → `/gallery/dallas`.
- `artists-featured.html` — 5 fullscreen panels (2 wired to existing pages; 3 stubs).
- `assets/css/home.css`, `assets/js/home.js` — minimal, defer‑loaded.
- Footer update: glitter icons + hidden interactive link; no runtime for game on `/`.

---

**Ready to implement**: follow §§2–4 for placements and new gateways; use §8 for task tracking. If you want, I can output the three HTML stubs and the scoped CSS/JS next.

