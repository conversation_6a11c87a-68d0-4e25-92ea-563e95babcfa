<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <title>Dallas 2025</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="description" content="Chocolate and Art show — Dallas — September 18 - 19 — already %60 sold out, this is going to be a good one." />
  <style>
    @import url(https://fonts.googleapis.com/css2?family=Bebas+Neue&display=swap);
    :root { --base-w:1000px; --base-h:562px; }
    * { box-sizing: border-box; }
    html, body { height:100%; }
    body {
      margin:0; padding:0; display:flex; align-items:center; justify-content:center;
      background:#000; overflow:hidden;
    }
    .card, .content, body { overflow:hidden; }
    .card, .container { display:flex; align-items:center; }
    .card { width:1280px; height:720px; margin:0; justify-content:center; }
    .container-full, .content { width: var(--base-w); height: var(--base-h); position:relative; }
    .content { border-radius:40px; animation:200s linear infinite brightness; }
    .container { justify-content:center; }
    .container-full, .face { display:flex; align-items:center; overflow:hidden; }
    .container-full { margin:0; justify-content:center; transform:scale(1); animation:200s linear infinite zoom-in; }
    .cube, .face { width:870px; height:190px; }
    .cube { position:relative; transform-style:preserve-3d; perspective:480px; perspective-origin:51% 70%; }
    .face, .hue { position:absolute; }
    .face { background:transparent; border:0 solid #000; opacity:.5; }
    .bottom, .top { width:870px; height:870px; }
    p {
      white-space:nowrap; overflow:hidden; margin:0;
      font-family:"Bebas Neue", sans-serif; font-weight:400;
      font-size: calc(6em + (190px / 10) * 0.7);
      padding-top:20px; color:#fff;
    }
    span { color:#ff3b3b; }
    .front { transform: translateZ(435px); display:none; }
    .back  { transform: translateZ(-435px) rotateY(180deg) scaleX(-1); }
    .left  { transform: translateX(-435px) rotateY(-90deg) scaleX(-1); }
    .right { transform: translateX( 435px) rotateY( 90deg) scaleX(-1); }
    .top   { transform: translateY(-435px) rotateX( 90deg) scaleY(-1); }
    .bottom{ transform: translateY(-245px) rotateX(-90deg) scaleY(-1); }
    .left  p { margin-left:  480px; animation:200s linear infinite left; }
    .back  p { margin-left: -390px; animation:200s linear infinite back; }
    .right p { margin-left:-1260px; animation:200s linear infinite right; }
    @keyframes left  { 100% { margin-left: -54000px; } }
    @keyframes back  { 100% { margin-left: -54870px; } }
    @keyframes right { 100% { margin-left: -55740px; } }
    .hue {
      top:0; left:0; z-index:3; width:100%; height:100%;
      mix-blend-mode:overlay;
      background: radial-gradient(ellipse at center, #1e5799 0%, #7db9e8 100%);
      opacity:1;
    }
    .hue.animated { animation:8s infinite filter-animation; }
    @keyframes filter-animation { 0%,100%{ filter:hue-rotate(0deg); } 50%{ filter:hue-rotate(100deg); } }
    .container-reflect {
      position:absolute; inset:auto 0 0 0; display:flex; align-items:center; justify-content:center;
      margin-top:380px; filter:blur(10px);
    }
    .container-reflect .cube { perspective-origin:51% -30%; }
    .container-reflect .back  { transform: translateZ(-435px) rotateY(180deg) scaleX(-1) scaleY(-1); }
    .container-reflect .left  { transform: translateX(-435px) rotateY(-90deg) scaleX(-1) scaleY(-1); }
    .container-reflect .right { transform: translateX( 435px) rotateY( 90deg) scaleX(-1) scaleY(-1); }
    .container-reflect p { transform: scaleY(70%); }
    @keyframes zoom-in { 0% {transform:scale(1);} 100% {transform:scale(2.5);} }
    @keyframes brightness { 0% { filter:brightness(1) contrast(1);} 100% { filter:brightness(.8) contrast(1.3);} }

    /* Top overlay text */
    .overlay {
      position:absolute; top:16px; left:20px; right:20px; z-index:4;
      display:flex; align-items:center; justify-content:space-between; color:#fff; font-family:system-ui,-apple-system,Segoe UI,Roboto,Ubuntu,"Helvetica Neue",Arial,sans-serif;
      text-transform:uppercase; letter-spacing:.06em; font-size:14px; opacity:.85;
      mix-blend-mode:screen;
    }
    .overlay .title { font-weight:700; }
    .overlay .status { font-weight:600; }
    .overlay .status .hot { color:#ff3b3b; }
    /* Mobile scale */
    @media (max-width: 1024px) {
      .overlay { font-size:12px; }
    }
  </style>
</head>
<body>
  <!-- Minimal adaptation of "Living Words" effect -->
  <div class="container">
    <div class="content" id="content" style="display:block;width:var(--base-w);height:var(--base-h)">
      <div class="container-full">
        <div class="hue animated"></div>

        <!-- 3D scrolling text cube -->
        <div class="container">
          <div class="cube">
            <div class="face top"></div>
            <div class="face bottom"></div>
            <div class="face left text"></div>
            <div class="face right text"></div>
            <div class="face front"></div>
            <div class="face back text"></div>
          </div>
        </div>

        <!-- Reflection -->
        <div class="container-reflect">
          <div class="cube">
            <div class="face top"></div>
            <div class="face bottom"></div>
            <div class="face left text"></div>
            <div class="face right text"></div>
            <div class="face front"></div>
            <div class="face back text"></div>
          </div>
        </div>

        <!-- Overlay header -->
        <div class="overlay" aria-hidden="true">
          <div class="title">Dallas 2025</div>
          <div class="status">Chocolate &amp; Art Show • <span>September 18–19</span> • <span class="hot">already %60 sold out</span></div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Message to display (repeated to create long scroll)
    const eventLine = "Chocolate and Art show Dallas — September 18 - 19 — already %60 sold out — this is going to be a good one — ";
    const repeated = "<p>" + Array(220).fill(eventLine).join(" ") + "</p>";

    // Insert into all .text faces
    function insertMsg() {
      document.querySelectorAll(".text").forEach(div => { div.innerHTML = repeated; });
    }

    // Responsive downscale for small viewports
    function adjustContentSize() {
      const contentDiv = document.getElementById("content");
      const vw = window.innerWidth;
      const base = 1000;
      const scale = vw < base ? (vw / base) * 0.8 : 1;
      contentDiv.style.transform = `scale(${scale})`;
    }

    window.addEventListener("DOMContentLoaded", () => {
      insertMsg();
      adjustContentSize();
    });
    window.addEventListener("resize", adjustContentSize);
    window.addEventListener("load", adjustContentSize);
  </script>
</body>
</html>

