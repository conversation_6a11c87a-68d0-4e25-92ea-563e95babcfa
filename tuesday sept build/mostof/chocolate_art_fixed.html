<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Chocolate & Art Show — Dallas | September 18-19, 2025</title>
  <meta name="description"
    content="Immersive art, live music, body painting, and artisan chocolate. Two nights in Dallas at Lofty Spaces. 21+ event featuring local artists and DJs.">

  <!-- Preconnect to external domains -->
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  <!-- Font loading -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;800&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Prompt:wght@800&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Monoton&display=swap" rel="stylesheet">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="assets/images/choco-logo.png">

  <style>
    :root {
      --pink: #ff2ebd;
      --pink-2: #ff7ad6;
      --white: #ffffff;
      --bg: #000;
      --card-bg: #1a1a1a;
      --card-bd: #333;
      --fg: #eef2ff;
      --name: #fff;
    }

    * { box-sizing: border-box; }
    html, body { height: 100%; margin: 0; padding: 0; }

    body {
      background: var(--bg);
      color: var(--white);
      font-family: "Inter", system-ui, sans-serif;
    }

    /* Navigation */
    .nav {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      background: rgba(0, 0, 0, 0.9);
      backdrop-filter: blur(10px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .nav-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 2rem;
      max-width: 1200px;
      margin: 0 auto;
    }

    .nav-logo {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: var(--white);
      text-decoration: none;
      font-weight: 800;
    }

    .nav-menu {
      display: flex;
      list-style: none;
      margin: 0;
      padding: 0;
      gap: 2rem;
    }

    .nav-menu a {
      color: var(--white);
      text-decoration: none;
      font-weight: 600;
      transition: color 0.3s ease;
    }

    .nav-menu a:hover {
      color: var(--pink);
    }

    /* Hero Section with Neon */
    .hero {
      position: relative;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      background: var(--bg);
    }

    .video-background {
      position: absolute;
      inset: 0;
      width: 100%;
      height: 100%;
    }

    .hero video {
      position: absolute;
      inset: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      filter: brightness(0.4);
      z-index: 1;
    }

    .overlay {
      position: absolute;
      inset: 0;
      background: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.8));
      z-index: 2;
    }

    .hero-content {
      position: relative;
      text-align: center;
      color: var(--white);
      z-index: 10;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 3rem;
      padding: 2rem;
    }

    /* Neon Sign */
    .neon-sign {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: clamp(0.4rem, 2.2vw, 1.25rem);
      text-align: center;
      margin-bottom: 2rem;
    }

    .neon-line {
      --stroke: rgba(255,255,255,0.85);
      font-family: "Monoton", ui-sans-serif, system-ui, -apple-system, "Segoe UI", Roboto, Inter, "Helvetica Neue", Arial, "Noto Sans";
      font-weight: 400;
      line-height: 0.88;
      text-transform: uppercase;
      white-space: nowrap;
      color: var(--white);
      text-rendering: optimizeLegibility;
      font-kerning: normal;
      text-shadow:
        0 0 .02em var(--white),
        0 0 .08em var(--white),
        0 0 .18em var(--pink),
        0 0 .36em var(--pink),
        0 0 .72em var(--pink),
        0 0 1.2em var(--pink-2);
      -webkit-text-stroke: .01em var(--stroke);
      position: relative;
      isolation: isolate;
      animation: flickerLoop 3.6s linear infinite, glow 6s ease-in-out infinite;
    }

    .line-1 { 
      font-size: clamp(2.8rem, 10.6vw, 10rem);
      letter-spacing: clamp(-.05em, -0.40vw, -.10em);
    }
    .line-2 { 
      font-size: clamp(2.6rem, 9.8vw, 9rem);
      letter-spacing: clamp(-.05em, -0.34vw, -.10em);
      word-spacing: clamp(0.02em, 0.2vw, 0.12em);
    }
    .line-3 { 
      font-size: clamp(2.4rem, 8.8vw, 8rem);
      letter-spacing: clamp(-.08em, -0.50vw, -.14em);
    }

    .neon-line::before,
    .neon-line::after {
      content: attr(data-text);
      position: absolute; 
      inset: 0; 
      z-index: -1;
      color: var(--pink);
      filter: blur(.12em);
      opacity: .55;
    }
    .neon-line::after { 
      filter: blur(.45em); 
      opacity: .35; 
    }

    /* Buy Tickets Section */
    .hero-cta {
      display: flex;
      flex-direction: column;
      gap: 2rem;
      align-items: center;
    }

    .cta-title {
      font-family: "Prompt", system-ui, sans-serif;
      font-weight: 800;
      line-height: 0.9;
      margin: 0;
      background: linear-gradient(4deg, #548cff 10%, #f900bf 90%);
      -webkit-text-fill-color: transparent;
      -webkit-background-clip: text;
      background-clip: text;
      --shadow-size: 0.08em;
      padding: var(--shadow-size) 0 0 var(--shadow-size);
      text-shadow: calc(-1 * var(--shadow-size)) calc(-1 * var(--shadow-size)) #fff;
      font-size: clamp(2.2rem, 8vw, 5rem);
      text-align: center;
    }

    .ticket-buttons {
      display: flex;
      gap: 2rem;
      flex-wrap: wrap;
      justify-content: center;
    }

    /* Textured Button Style from CodePen */
    .btn-textured {
      position: relative;
      display: inline-block;
      padding: 1.5rem 3rem;
      font-size: clamp(1.2rem, 3vw, 1.8rem);
      font-weight: 800;
      text-transform: uppercase;
      letter-spacing: 0.1em;
      color: #fff;
      text-decoration: none;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      overflow: hidden;
      
      /* Textured background */
      background: 
        radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(120, 200, 255, 0.3) 0%, transparent 50%),
        linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      
      box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
      
      border-radius: 8px;
    }

    .btn-textured::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    .btn-textured:hover::before {
      left: 100%;
    }

    .btn-textured:hover {
      transform: translateY(-2px);
      box-shadow: 
        0 12px 40px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    .btn-textured:active {
      transform: translateY(0);
    }

    /* RGB Wall */
    .rgb-wall {
      isolation: isolate;
      min-height: 58vh;
      display: grid;
      place-items: center;
      gap: 2rem;
      padding: 8vh 4vw;
      background: #0f1114;
    }

    .rgb-hero {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: clamp(0.4rem, 2vw, 1.2rem);
      justify-content: center;
    }

    .rgb-plus, .rgb-eq {
      color: #8ea3b3;
      font-weight: 800;
      font-size: clamp(2rem, 8vw, 6rem);
    }

    .rgb-word {
      position: relative;
      display: inline-block;
      font-weight: 900;
      text-transform: uppercase;
      letter-spacing: -0.02em;
      line-height: 0.85;
      color: #eaeaea;
      font-size: clamp(3rem, 14vw, 12rem);
    }

    .rgb-word::before,
    .rgb-word::after {
      content: attr(data-txt);
      position: absolute;
      inset: 0;
      z-index: -1;
    }

    .rgb-word::before {
      transform: translate(0.18em, 0.06em);
      color: #e53935;
      opacity: 0.8;
    }

    .rgb-word::after {
      transform: translate(-0.14em, 0.08em);
      color: #03a9f4;
      opacity: 0.75;
    }

    /* Animations */
    @keyframes flickerLoop {
      0%,100% { opacity: 1 }
      2%   { opacity: .25 }
      3%   { opacity: 1 }
      5%   { opacity: .4 }
      8%   { opacity: 1 }
      10%  { opacity: .35 }
      12%  { opacity: 1 }
      25%  { opacity: .9 }
      28%  { opacity: .5 }
      30%  { opacity: 1 }
      60%  { opacity: .92 }
      62%  { opacity: .55 }
      70%  { opacity: 1 }
      90%  { opacity: .97 }
    }

    @keyframes glow {
      0%,100% { 
        text-shadow:
          0 0 .03em var(--white),
          0 0 .06em var(--white),
          0 0 .15em var(--pink),
          0 0 .32em var(--pink),
          0 0 .6em  var(--pink),
          0 0 1.1em var(--pink-2); 
      }
      50% { 
        text-shadow:
          0 0 .02em var(--white),
          0 0 .04em var(--white),
          0 0 .12em var(--pink),
          0 0 .26em var(--pink),
          0 0 .48em var(--pink),
          0 0 .9em  var(--pink-2); 
      }
    }

    /* Gallery Teaser */
    .gallery-teaser {
      padding: 4rem 0;
      background: var(--bg);
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .gallery-teaser h2 {
      text-align: center;
      margin-bottom: 3rem;
      color: var(--fg);
      font-size: 2.5rem;
      font-weight: 800;
    }

    .gallery-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin-bottom: 3rem;
    }

    .gallery-item {
      margin: 0;
      background: var(--card-bg);
      border: 1px solid var(--card-bd);
      border-radius: 16px;
      overflow: hidden;
      transition: transform 0.3s ease;
    }

    .gallery-item:hover {
      transform: translateY(-4px);
    }

    .gallery-item img {
      width: 100%;
      height: 250px;
      object-fit: cover;
      display: block;
    }

    .gallery-item figcaption {
      padding: 1rem;
      font-weight: 600;
      color: var(--name);
      text-align: center;
    }

    .gallery-cta {
      text-align: center;
    }

    .btn-secondary {
      display: inline-block;
      padding: 1rem 2rem;
      background: transparent;
      border: 2px solid var(--pink);
      color: var(--pink);
      text-decoration: none;
      border-radius: 8px;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .btn-secondary:hover {
      background: var(--pink);
      color: var(--white);
    }

    /* Footer */
    .footer {
      background: #111;
      padding: 4rem 0 2rem;
      border-top: 1px solid #333;
    }

    .footer-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-bottom: 2rem;
    }

    .footer-section h3 {
      color: var(--pink);
      margin-bottom: 1rem;
    }

    .footer-section ul {
      list-style: none;
      padding: 0;
    }

    .footer-section li {
      margin-bottom: 0.5rem;
    }

    .footer-section a {
      color: #ccc;
      text-decoration: none;
      transition: color 0.3s ease;
    }

    .footer-section a:hover {
      color: var(--pink);
    }

    .footer-logo {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 1rem;
    }

    .footer-tagline {
      color: #ccc;
      font-style: italic;
    }

    .footer-bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 2rem;
      border-top: 1px solid #333;
      flex-wrap: wrap;
      gap: 1rem;
    }

    .footer-legal p {
      margin: 0.25rem 0;
      color: #888;
      font-size: 0.9rem;
    }

    .footer-social {
      display: flex;
      gap: 1rem;
    }

    .footer-social a {
      font-size: 1.2rem;
      text-decoration: none;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
      .nav-container {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
      }
      
      .nav-menu {
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
      }
      
      .ticket-buttons {
        flex-direction: column;
        align-items: center;
      }
      
      .btn-textured {
        padding: 1.2rem 2.5rem;
        font-size: 1.2rem;
      }
      
      .gallery-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }
      
      .footer-bottom {
        flex-direction: column;
        text-align: center;
      }
    }

    /* Reduced Motion */
    @media (prefers-reduced-motion: reduce) {
      .neon-line {
        animation: none;
      }
      
      .gallery-item {
        transition: none;
      }
      
      .gallery-item:hover {
        transform: none;
      }
      
      .btn-textured::before {
        display: none;
      }
    }
  </style>
</head>

<body>
  <!-- Skip to main content for accessibility -->
  <a href="#main-content" class="sr-only">Skip to main content</a>

  <!-- Navigation -->
  <nav class="nav" role="navigation" aria-label="Main navigation">
    <div class="nav-container">
      <a href="index.html" class="nav-logo" aria-label="Chocolate & Art Show home">
        <img src="assets/images/choco-logo.png" alt="Chocolate & Art Show logo" width="40" height="40">
        <span>Chocolate & Art</span>
      </a>

      <ul class="nav-menu" role="menubar">
        <li role="none">
          <a href="index.html" role="menuitem">Home</a>
        </li>
        <li role="none">
          <a href="artists/index.html" role="menuitem">Artists</a>
        </li>
        <li role="none">
          <a href="tickets.html" role="menuitem">Tickets</a>
        </li>
        <li role="none">
          <a href="gallery.html" role="menuitem">Gallery</a>
        </li>
        <li role="none">
          <a href="schedule.html" role="menuitem">Schedule</a>
        </li>
        <li role="none">
          <a href="faq.html" role="menuitem">FAQ</a>
        </li>
        <li role="none">
          <a href="contact.html" role="menuitem">Contact</a>
        </li>
      </ul>
    </div>
  </nav>

<!-- Main Content -->
<main id="main-content">
  <!-- Hero Section -->
  <section class="hero" aria-label="Dallas Hero">
    <div class="overlay" aria-hidden="true"></div>

    <!-- Video Background -->
    <div class="video-background">
      <video id="chocoArtVideo" preload="auto" autoplay muted playsinline loop 
             poster="assets/images/hero-chocolate-art-tonight.png">
        <source src="assets/videos/chocoartshow.mp4" type="video/mp4">
        <img src="assets/images/hero-chocolate-art-tonight.png" 
             alt="Chocolate and Art Show fallback image" loading="lazy">
      </video>
    </div>

    <div class="hero-content">
      <!-- Neon Sign -->
      <div class="neon-sign">
        <div class="neon-line line-1" data-text="CHOCOLATE">CHOCOLATE</div>
        <div class="neon-line line-2" data-text="AND&nbsp;ART">AND&nbsp;ART</div>
        <div class="neon-line line-3" data-text="SHOW">SHOW</div>
      </div>

      <!-- Buy Tickets CTA -->
      <div class="hero-cta">
        <h2 class="cta-title">BUY TICKETS DALLAS</h2>
        <div class="ticket-buttons">
          <a class="btn-textured"
             href="https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089"
             rel="noopener noreferrer" aria-label="Buy Thursday tickets on Eventbrite">
            BUY TICKETS THURSDAY
          </a>
          <a class="btn-textured"
             href="https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089"
             rel="noopener noreferrer" aria-label="Buy Friday tickets on Eventbrite">
            BUY TICKETS FRIDAY
          </a>
        </div>
      </div>
    </div>
  </section>

  <script>
    const vid = document.getElementById("chocoArtVideo");
    vid.addEventListener("loadedmetadata", () => {
      if (vid.duration > 33) {
        vid.currentTime = 33;
      }
    });
  </script>
</main>

<!-- RGB Big-Type Interstitial Wall -->
<section class="rgb-wall" aria-label="Intermission banner">
  <div class="rgb-hero">
    <span class="rgb-word" data-txt="ART">ART</span>
    <span class="rgb-plus">+</span>
    <span class="rgb-word" data-txt="MUSIC">MUSIC</span>
    <span class="rgb-plus">+</span>
    <span class="rgb-word" data-txt="CHOCOLATE">CHOCOLATE</span>
    <span class="rgb-eq">=</span>
    <span class="rgb-word" data-txt="DALLAS">DALLAS</span>
  </div>
  <a class="btn-textured" 
     href="https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089"
     rel="noopener noreferrer">
    BUY TICKETS
  </a>
</section>

<!-- Gallery Teaser -->
<section class="gallery-teaser" aria-label="Gallery preview">
  <div class="container">
    <h2>Experience the Art</h2>
    <div class="gallery-grid">
      <figure class="gallery-item">
        <img src="assets/gallery/teaser/1.jpg" alt="Live art creation at Chocolate & Art Show" loading="lazy"
          width="400" height="300">
        <figcaption>Live Art Creation</figcaption>
      </figure>
      <figure class="gallery-item">
        <img src="assets/gallery/teaser/2.jpg" alt="Body art performance" loading="lazy" width="400" height="300">
        <figcaption>Body Art</figcaption>
      </figure>
      <figure class="gallery-item">
        <img src="assets/gallery/teaser/3.jpg" alt="Live music performance" loading="lazy" width="400" height="300">
        <figcaption>Live Music</figcaption>
      </figure>
      <figure class="gallery-item">
        <img src="assets/gallery/teaser/4.jpg" alt="Artisan chocolate display" loading="lazy" width="400"
          height="300">
        <figcaption>Artisan Chocolate</figcaption>
      </figure>
      <figure class="gallery-item">
        <img src="assets/gallery/teaser/5.jpg" alt="Interactive art experience" loading="lazy" width="400"
          height="300">
        <figcaption>Interactive Experience</figcaption>
      </figure>
      <figure class="gallery-item">
        <img src="assets/gallery/teaser/6.jpg" alt="Night atmosphere at the show" loading="lazy" width="400"
          height="300">
        <figcaption>Night Atmosphere</figcaption>
      </figure>
    </div>
    <div class="gallery-cta">
      <a href="gallery.html" class="btn-secondary">View Full Gallery</a>
    </div>
  </div>
</section>

<!-- Footer -->
<footer class="footer">
  <div class="container">
    <div class="footer-content">
      <div class="footer-section">
        <div class="footer-logo">
          <img src="assets/images/choco-logo.png" alt="Chocolate & Art Show logo" width="40" height="40">
          <span>Chocolate & Art Show</span>
        </div>
        <p class="footer-tagline">Where art meets chocolate, and creativity flows.</p>
      </div>

      <div class="footer-section">
        <h3>Event Info</h3>
        <ul>
          <li>Thursday & Friday, September 18-19, 2025</li>
          <li>Lofty Spaces, Dallas</li>
          <li>21+ Event</li>
          <li>Doors: 7:00 PM</li>
        </ul>
      </div>

      <div class="footer-section">
        <h3>Quick Links</h3>
        <ul>
          <li><a href="tickets.html">Buy Tickets</a></li>
          <li><a href="artists/index.html">Meet Artists</a></li>
          <li><a href="schedule.html">Event Schedule</a></li>
          <li><a href="gallery.html">Photo Gallery</a></li>
        </ul>
      </div>

      <div class="footer-section">
        <h3>Get Involved</h3>
        <ul>
          <li><a href="mailto:<EMAIL>?subject=Artist Application">Apply as Artist</a></li>
          <li><a href="mailto:<EMAIL>?subject=Vendor Application">Apply as Vendor</a></li>
          <li><a href="contact.html">Contact Us</a></li>
        </ul>
      </div>
    </div>

    <div class="footer-bottom">
      <div class="footer-legal">
        <p>&copy; 2025 Chocolate & Art Show. All rights reserved.</p>
        <p>An immersive experience celebrating art, music, and artisan chocolate.</p>
      </div>
      <div class="footer-social">
        <a href="mailto:<EMAIL>" aria-label="Email us">
          <span>📧</span>
        </a>
        <a href="play/kusama/index.html" aria-label="Interactive playground">
          <span>🎨</span>
        </a>
      </div>
    </div>
  </div>
</footer>

<!-- Scripts -->
<script>
  // Video handling
  document.addEventListener('DOMContentLoaded', function() {
    const video = document.getElementById('chocoArtVideo');
    if (video) {
      video.play().catch(e => console.log('Video autoplay failed:', e));
    }
  });
</script>

</body>

</html>