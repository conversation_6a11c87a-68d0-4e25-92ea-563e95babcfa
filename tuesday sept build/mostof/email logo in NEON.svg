<svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg" fill="none">
  <defs>
    <filter id="neon-glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="2.5" result="blur"/>
      <feFlood flood-color="#39FF14" result="color"/>
      <feComposite in="color" in2="blur" operator="in" result="glow"/>
      <feMerge>
        <feMergeNode in="glow"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Envelope Body -->
  <rect x="6" y="18" width="52" height="28" rx="4" stroke="#39FF14" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" filter="url(#neon-glow)"/>

  <!-- Envelope Flap -->
  <path d="M6 18 L32 36 L58 18" stroke="#39FF14" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" filter="url(#neon-glow)"/>

  <!-- Detail Lines -->
  <line x1="6" y1="18" x2="32" y2="34" stroke="#39FF14" stroke-width="1.5" opacity="0.7" filter="url(#neon-glow)"/>
  <line x1="58" y1="18" x2="32" y2="34" stroke="#39FF14" stroke-width="1.5" opacity="0.7" filter="url(#neon-glow)"/>

  <!-- Neon Glow Core -->
  <rect x="6" y="18" width="52" height="28" rx="4" fill-opacity="0" stroke-opacity="0.2" stroke="#39FF14" stroke-width="8" filter="url(#neon-glow)"/>
</svg>