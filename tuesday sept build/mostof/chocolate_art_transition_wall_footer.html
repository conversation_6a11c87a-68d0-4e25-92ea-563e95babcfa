<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<title>Chocolate & Art — Transition Wall + Footer</title>
<style>
  :root{
    --pink:#ff2ebd; --pink-2:#ff7ad6;
    --ink:#eaeaf0; --ink-dim:#bfc3cf;
    --bg:#0f1114; --glass: rgba(10,10,14,.55);
  }
  *{box-sizing:border-box} html,body{height:100%} body{margin:0;background:#000;color:#fff;font:500 16px/1.35 ui-sans-serif,system-ui,-apple-system,"Segoe UI",Roboto,Inter,Arial}
  /* ===== Fixed JS-free header (nav) ===== */
  .site-header{position:fixed;z-index:10;inset-inline:0;top:0;height:62px;backdrop-filter:blur(8px);-webkit-backdrop-filter:blur(8px);background:var(--glass);border-bottom:1px solid rgba(255,255,255,.06)}
  .nav{max-width:min(1200px,92vw);margin:0 auto;height:100%;display:flex;align-items:center;gap:16px}
  .brand{display:flex;align-items:center;gap:10px;text-decoration:none;color:var(--ink);font-weight:800;letter-spacing:.04em}
  .brand .dot{width:12px;height:12px;border-radius:50%;background:var(--pink);box-shadow:0 0 12px var(--pink)}
  .menu{margin-left:auto;display:flex;align-items:center;gap:4px}
  .menu a{position:relative;display:inline-grid;place-items:center;height:40px;padding:0 14px;border-radius:10px;text-decoration:none;color:var(--ink-dim);letter-spacing:.02em}
  .menu a:hover,.menu a:focus-visible{color:#fff}
  .menu a::after{content:"";position:absolute;left:10px;right:10px;bottom:6px;height:2px;border-radius:2px;background:linear-gradient(90deg,transparent,var(--pink),transparent);box-shadow:0 0 10px var(--pink),0 0 18px var(--pink-2);opacity:0;transform:scaleX(.8);transition:opacity .18s ease,transform .18s ease}
  .menu a:hover::after,.menu a:focus-visible::after{opacity:.85;transform:scaleX(1)}
  .menu a[aria-current="page"]{color:#fff;outline:1px solid rgba(255,122,214,.38);background:rgba(255,46,189,.05);box-shadow:inset 0 0 18px rgba(255,46,189,.18)}
  @media (prefers-reduced-motion:reduce){.menu a::after{transition:none}}
  /* push content below fixed header */
  main{padding-top:72px}

  /* ===== RGB Big-Type Wall (AS IS) ===== */
  .rgb-wall{isolation:isolate;min-height:58vh;display:grid;place-items:center;gap:2rem;padding:8vh 4vw;background:var(--bg)}
  .rgb-hero{display:flex;flex-wrap:wrap;align-items:center;gap:clamp(.4rem,2vw,1.2rem);justify-content:center}
  .rgb-plus,.rgb-eq{color:#8ea3b3;font-weight:800;font-size:clamp(2rem,8vw,6rem)}
  .rgb-word{position:relative;display:inline-block;font-weight:900;text-transform:uppercase;letter-spacing:-.02em;line-height:.85;color:#eaeaea;font-size:clamp(3rem,14vw,12rem)}
  .rgb-word::before,.rgb-word::after{content:attr(data-txt);position:absolute;inset:0;z-index:-1}
  .rgb-word::before{transform:translate(.18em,.06em);color:#e53935;opacity:.8}
  .rgb-word::after{transform:translate(-.14em,.08em);color:#03a9f4;opacity:.75}

  /* ===== Footer + Confetti trigger ===== */
  .site-footer{background:#0a0a0a;color:#eaeaea;padding:24px 16px}
  .footer-inner{display:flex;flex-wrap:wrap;align-items:center;gap:16px;justify-content:space-between;max-width:1200px;margin:0 auto}
  .footer-nav{display:flex;gap:16px;flex-wrap:wrap}
  .footer-nav a{color:#cfcfcf;text-decoration:none;font-size:.95rem}
  .footer-nav a:hover{color:#fff}
  .brand-ft{display:inline-flex;align-items:center;gap:8px;color:inherit;text-decoration:none;font-weight:600}
  .footer-legal{max-width:1200px;margin:10px auto 0;padding-top:8px;border-top:1px solid #ffffff15;color:#bdbdbd;font-size:.85rem;text-align:center}
  .btn-modern{position:relative;display:inline-grid;place-items:center;padding:0;height:46px;min-width:220px;border:1px solid rgba(255,255,255,.12);background:linear-gradient(180deg,#222,#0f0f0f);color:#fff;cursor:pointer;user-select:none;overflow:hidden;border-radius:0;box-shadow:0 12px 24px rgba(0,0,0,.35),0 0 0 1px rgba(255,255,255,.06),inset 0 1px 0 rgba(255,255,255,.08),inset 0 -1px 0 rgba(0,0,0,.6);transition:box-shadow .18s ease,transform .12s ease,background .18s ease}
  .btn-modern__inner{position:relative;z-index:2;padding:0 18px;font-weight:800;letter-spacing:.02em}
  .btn-modern::after{content:"";position:absolute;inset:0;background:linear-gradient(180deg,rgba(255,255,255,.06),transparent 40% 60%,rgba(255,255,255,.02));mix-blend-mode:screen;pointer-events:none}
  .btn-modern:hover{box-shadow:0 16px 28px rgba(0,0,0,.4),0 0 0 1px rgba(255,255,255,.08),inset 0 1px 0 rgba(255,255,255,.1),inset 0 -1px 0 rgba(0,0,0,.65)}
  .btn-modern:active{transform:translateY(1px)}
  .confetti-canvas{position:fixed;inset:0;pointer-events:none;z-index:9999;display:block}

  /* ===== Glitter Social Icons ===== */
  .footer-social.glitter-icons{display:flex;gap:.75rem;align-items:center}
  .gi{--size:40px;--icon:none;--ring:1.25px;position:relative;display:inline-block;width:var(--size);aspect-ratio:1;border-radius:12px;background:linear-gradient(#0b0c10,#0b0c10);border:1px solid hsl(0 0% 100% / .08);box-shadow:0 8px 24px hsl(0 0% 0% / .5) inset,0 6px 24px hsl(0 0% 0% / .35);text-decoration:none;transition:transform .2s ease,filter .2s ease}
  .gi:hover{transform:translateY(-1px)}
  .gi::before{content:"";position:absolute;inset:16%;background:
    radial-gradient(circle at 16% 22%,#fff 0 1.5px,#0000 2px),
    radial-gradient(circle at 64% 70%,#fff 0 1px,#0000 1.75px),
    radial-gradient(circle at 72% 28%,#fff 0 1.25px,#0000 1.75px),
    radial-gradient(circle at 38% 56%,#fff 0 1.25px,#0000 1.75px),
    conic-gradient(from var(--angle,0deg),#ff7ad9,#8fb3ff,#6affc4,#ffe38a,#ff7ad9);
    background-size:auto,auto,auto,auto,200% 200%;mix-blend-mode:plus-lighter;mask:var(--icon) center/contain no-repeat;opacity:.9;animation:shimmer 14s linear infinite paused}
  .gi:hover::before,.gi:focus-visible::before{animation-play-state:running}
  .gi::after{content:"";position:absolute;inset:0;border-radius:inherit;padding:var(--ring);background:conic-gradient(from var(--angle,0deg),#ff7ad9,#8fb3ff,#6affc4,#ffe38a,#ff7ad9);-webkit-mask:linear-gradient(#000 0 0) content-box,linear-gradient(#000 0 0);-webkit-mask-composite:xor;mask-composite:exclude;opacity:.85;animation:shimmer 14s linear infinite paused}
  .gi:hover::after,.gi:focus-visible::after{animation-play-state:running}
  .gi--fb{--icon:url('/assets/icons/facebook.svg')}
  .gi--ig{--icon:url('/assets/icons/instagram.svg')}
  .gi--eb{--icon:url('/assets/icons/eventbrite.svg')}
  .gi--mail{--icon:url('/assets/icons/minutemailer.svg')}
  @keyframes shimmer{to{--angle:360deg;background-position:200% 200%}}
  .sr-only{position:absolute;width:1px;height:1px;margin:-1px;border:0;padding:0;clip:rect(0 0 0 0);overflow:hidden}
  @media (prefers-reduced-motion:reduce){.gi::before,.gi::after{animation:none!important}}
  /* ---- Fixes 0822: Footer alignment + Celebrate button theme ---- */
  .footer-inner{display:grid;grid-template-columns:1fr auto auto auto;gap:20px;align-items:center}
  .footer-celebrate{justify-self:end}
  @media (max-width:800px){
    .footer-inner{grid-template-columns:1fr}
    .footer-celebrate{justify-self:start;margin-top:8px}
  }
  .btn-modern{background:var(--glass);color:var(--ink);border:1px solid rgba(255,255,255,.12);box-shadow:0 12px 24px rgba(0,0,0,.35),0 0 0 1px rgba(255,255,255,.06),inset 0 1px 0 rgba(255,255,255,.08),inset 0 -1px 0 rgba(0,0,0,.6)}
  .btn-modern__inner{color:var(--ink)}
  .btn-modern::before{content:"";position:absolute;left:12px;right:12px;bottom:8px;height:2px;border-radius:2px;background:linear-gradient(90deg,transparent,var(--pink),transparent);box-shadow:0 0 10px var(--pink),0 0 18px var(--pink-2);opacity:.9}
  .btn-modern:hover{box-shadow:0 16px 28px rgba(0,0,0,.4),0 0 0 1px rgba(255,255,255,.08),inset 0 1px 0 rgba(255,255,255,.1),inset 0 -1px 0 rgba(0,0,0,.65),0 0 18px rgba(255,46,189,.25)}
  /* ---- Fixes 0822B: Footer/header text wrap + quick links + ghost buttons ---- */
  .brand span,.brand-ft span{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}
  .footer-inner>*{min-width:0}
  /* ghost variant for ticket CTAs (anchor-compatible) */
  .btn-ghost{background:transparent;border-color:rgba(255,255,255,.18)}
  .btn-ghost:hover{background:rgba(255,255,255,.06)}
  .footer-quick{display:flex;gap:12px;flex-wrap:wrap;align-items:center;margin-top:8px}
  .footer-quick a{color:#cfcfcf;text-decoration:none;font-size:.9rem}
  .footer-quick a:hover{color:#fff}
</style>
</head>
<body>

<header class="site-header">
  <nav class="nav" aria-label="Primary">
    <a class="brand" href="/" aria-label="Chocolate & Art home">
      <span class="dot" aria-hidden="true"></span><span>Chocolate & Art</span>
    </a>
    <div class="menu">
      <!-- MapTree parity: added Events & Cities ▼; kept existing routes -->
      <a href="/" aria-current="page">Home</a>
      <a href="/about">About</a>
      <a href="/events">Events & Cities ▼</a>
      <a href="/artists">Artists</a>
      <a href="/tickets">Tickets</a>
      <a href="/gallery/dallas">Gallery</a>
      <a href="/schedule/dallas">Schedule (Dallas)</a>
      <a href="/faq/dallas">FAQ (Dallas)</a>
      <a href="/contact">Contact</a>
      <!-- In‑page anchors exist on Home: /#about and /#testimonials -->
    </div>
  </nav>
</header>

<main>
  <!-- ===== Transition Wall (leave AS IS) ===== -->
  <section class="rgb-wall" aria-label="Intermission banner">
    <div class="rgb-hero">
      <span class="rgb-word" data-txt="ART">ART</span>
      <span class="rgb-plus">+</span>
      <span class="rgb-word" data-txt="MUSIC">MUSIC</span>
      <span class="rgb-plus">+</span>
      <span class="rgb-word" data-txt="CHOCOLATE">CHOCOLATE</span>
      <span class="rgb-eq">=</span>
      <span class="rgb-word" data-txt="DALLAS">DALLAS</span>
    </div>
  </section>
</main>

<!-- ===== Footer with confetti + glitter social buttons ===== -->
<footer class="site-footer" id="site-footer" role="contentinfo">
  <div class="footer-inner">
    <a class="brand-ft" href="/" aria-label="Chocolate & Art Show home">
      <img src="/assets/images/choco-logo.png" width="28" height="28" alt="Chocolate & Art Show" />
      <span>Chocolate & Art Show</span>
    </a>

    <nav class="footer-nav" aria-label="Footer navigation">
      <!-- MapTree parity: added Schedule (Dallas) + FAQ (Dallas) -->
      <a href="/about">About</a>
      <a href="/artists">Artists</a>
      <a href="/tickets">Tickets</a>
      <a href="/gallery/dallas">Gallery</a>
      <a href="/schedule/dallas">Schedule (Dallas)</a>
      <a href="/faq/dallas">FAQ (Dallas)</a>
      <a href="/contact">Contact</a>
    </nav>

    <!-- New glitter socials -->
    <nav class="footer-social glitter-icons" aria-label="Social links">
      <a class="gi gi--fb" href="https://www.facebook.com/ChocolateAndArtShowDallas/" target="_blank" rel="noopener" aria-label="Facebook"><span class="sr-only">Facebook</span></a>
      <a class="gi gi--ig" href="https://www.instagram.com/chocolateandartshow/" target="_blank" rel="noopener" aria-label="Instagram"><span class="sr-only">Instagram</span></a>
      <a class="gi gi--eb" href="https://www.eventbrite.com/e/chocolate-and-art-show-dallas-tickets-1354176742089" target="_blank" rel="noopener" aria-label="Eventbrite"><span class="sr-only">Eventbrite</span></a>
      <a class="gi gi--mail" href="mailto:<EMAIL>" aria-label="Email"><span class="sr-only">Email</span></a>
    </nav>

    <div class="footer-celebrate">
      <!-- Button theme aligned to header (0822 Fix) -->
      <button class="btn-modern" id="footer-confetti-trigger" aria-label="Celebrate Dallas">
        <span class="btn-modern__inner">Celebrate Dallas</span>
      </button>
      <!-- Tickets deep links (need exact Eventbrite URLs) -->
      <div class="footer-quick" aria-label="Quick links">
        <a class="btn-modern btn-ghost" href="#TODO-Eventbrite-Thursday" target="_blank" rel="noopener">Tickets (Thu)</a>
        <a class="btn-modern btn-ghost" href="#TODO-Eventbrite-Friday" target="_blank" rel="noopener">Tickets (Fri)</a>
        <!-- Home anchor jumps required by sitemap -->
        <a href="/#about">About ↓</a>
        <a href="/#testimonials">Testimonials ↓</a>
      </div>
    </div>
  </div>
  <div class="footer-legal">
    <small>© 2025 Chocolate & Art Show · 21+ (ID required) · Lofty Spaces · Sept 18–19</small>
  </div>
</footer>

<canvas id="confetti-canvas" class="confetti-canvas" aria-hidden="true"></canvas>
<script>
/* confetti aimed toward screen center from the button */
(function(){
  var t=document.getElementById("footer-confetti-trigger"),
      c=document.getElementById("confetti-canvas");
  if(!t||!c) return;
  var reduce=window.matchMedia&&window.matchMedia("(prefers-reduced-motion: reduce)").matches,
      save=navigator.connection&&navigator.connection.saveData===true,
      lowmem=navigator.deviceMemory&&Number(navigator.deviceMemory)<=2;
  if(reduce||save||lowmem) return;
  var cx,cy,ctx=c.getContext("2d");
  function resize(){ c.width=innerWidth; c.height=innerHeight; cx=c.width/2; cy=c.height/2 }
  resize(); addEventListener("resize",resize);
  function Particle(x,y){
    var ang=Math.atan2(cy-y,cx-x)+(Math.random()-.5)*.5, sp=7+Math.random()*5;
    this.x=x; this.y=y; this.vx=Math.cos(ang)*sp; this.vy=Math.sin(ang)*sp;
    this.s=7+Math.random()*7; this.r=Math.random()*Math.PI; this.vr=(Math.random()-.5)*.25;
    this.c=["#fff","#f8e16b","#9d7cff","#8ad5ff","#ff8a8a"][Math.random()*5|0]; this.a=1;
    this.tri=Math.random()<.5;
  }
  Particle.prototype.upd=function(){
    var dx=cx-this.x, dy=cy-this.y, d=Math.hypot(dx,dy)||1, n=Math.min(.015,.0009*d);
    this.vx+=dx/d*n; this.vy+=dy/d*n; this.vx*=.985; this.vy*=.985; this.x+=this.vx; this.y+=this.vy;
    this.r+=this.vr; this.a*=.986;
  }
  Particle.prototype.draw=function(){
    ctx.globalAlpha=this.a>0?this.a:0; ctx.save(); ctx.translate(this.x,this.y); ctx.rotate(this.r); ctx.fillStyle=this.c;
    if(this.tri){ ctx.beginPath(); ctx.moveTo(0,-this.s/2); ctx.lineTo(this.s/2,this.s/2); ctx.lineTo(-this.s/2,this.s/2); ctx.closePath(); ctx.fill(); }
    else ctx.fillRect(-this.s/2,-this.s/2,this.s,this.s*.7);
    ctx.restore();
  }
  function confetti(opts){
    var start=performance.now(), dur=(opts&&opts.duration)||1000, parts=[], n=(opts&&opts.particleCount)||200,
        x=(opts&&opts.x)||c.width/2, y=(opts&&opts.y)||c.height-80;
    for(var i=0;i<n;i++) parts.push(new Particle(x,y));
    function frame(ts){
      ctx.clearRect(0,0,c.width,c.height);
      for(var i=0;i<parts.length;i++){ parts[i].upd(); parts[i].draw(); }
      if(ts-start<dur) requestAnimationFrame(frame); else ctx.clearRect(0,0,c.width,c.height);
    }
    requestAnimationFrame(frame);
  }
  t.addEventListener("click",function(){
    var b=t.getBoundingClientRect(); confetti({x:b.left+b.width/2,y:b.top+b.height/2});
  });
})();
</script>

</body>
</html>
