# Chocolate & Art — Focused Implementation Plan

Scope: apply only the requested changes across the new full tree. Keep existing content/structure, but standardize visual language and interlinking. Respect project rule: **CSS in `/assets`, index & JS at root**.

---

## A) What we’re changing (named list)
1) **Brand tokens & colors** — add JSON + CSS vars and use everywhere.
2) **Navigation (neon, with logo)** — keep neon style; just point colors to tokens.
3) **Cards (boxes)** — unify to **Black & White Classico** hover style, **no accordion**.
4) **CTA pedestal** — use the new **diamond-with-hole** purple asset behind primary CTAs (non-blocking decoration).
5) **Gallery section** — make it a **full-screen scroll-snap section** (content unchanged).
6) **Artist Testimonials (new)** — **interlink-only** stub now; add to sitemap and primary interlink graph.
7) **Interstitial photos** — add light-weight images of **art + people** between scrolls (1–2 per page max).

---

## B) Brand tokens (colors) — add & map
**Create** `/assets/brand/colors.json`:
```json
{
  "//": "Chocolate & Art — brand tokens (neon + charcoal palette)",
  "ink": "#EAEAF0",
  "ink_dim": "#BFC3CF",
  "charcoal": "#0B0B0E",
  "panel": "#16161B",
  "border": "rgba(255,255,255,0.06)",
  "neon_primary": "#8B5CF6",
  "neon_halo": "#C4B5FD",
  "accent_orange": "#FFA94D"
}
```
**Update** `/assets/css/theme.css` (top of file):
```css
:root{
  /* from /assets/brand/colors.json */
  --ink:#EAEAF0;           /* body/nav text */
  --ink-dim:#BFC3CF;       /* subdued text */
  --bg:#0B0B0E;            /* page charcoal */
  --panel:#16161B;         /* card surface */
  --border:rgba(255,255,255,.06);
  --neon:#8B5CF6;          /* primary neon purple */
  --neon-2:#C4B5FD;        /* halo */
  --accent-orange:#FFA94D; /* micro-highlights */
}
```
> Comment this block: **“COLORS – branding”** so future editors know tokens map to the uploaded palette.

---

## C) File adds/updates (by path)
- **Add** `/assets/css/components/cards.css` — unified card style.
- **Add** `/assets/css/components/snap.css` — scoped snap rules.
- **Update** layout header in `index.html` (or shared partial) — keep logo, neon.
- **Add** pedestal asset `/assets/decors/cubo.png` (or `morado.png`).
- **Add** Testimonials stub page **`/testimonials/index.html`** **OR** anchor **`/artists#testimonials`** (choose one; sitemap reflects choice).
- **Add** lightweight interstitial image partial **`/partials/interstitial-photo.html`** used between sections.

---

## D) Navigation (keep neon & logo)
- Keep current structure and logo.
- Ensure menu includes: **Home, About, Events & Cities, Tickets, Artists, Gallery, Testimonials, Contact**.
- Colors: all neon glows read from `--neon` / `--neon-2`; text from `--ink`.
- Accessibility: preserve focus-visible glow using the halo token.

**Action:** Replace hard-coded colors in nav CSS with tokens above.

---

## E) Cards — unify to “BW Classico” (no accordion)
- Replace all section box UIs with a single class: `.card` within `.card-grid`.
- **Straight lines (no curves)**, subtle depth, neon lift on hover, no movement gimmicks.
- Remove/disable any accordion JS/CSS previously attached to cards.

**Hook points to convert:**
- Home feature boxes → `.card`.
- Event city tiles → `.card`.
- Artist teaser boxes → `.card`.
- FAQ/Info callouts → `.card`.

**CSS location:** `/assets/css/components/cards.css`.

---

## F) CTA pedestal (diamond-with-hole purple)
- Decorative **behind** main CTAs (hero and key interstitials).
- Use `<img>` with `aria-hidden="true"` and `loading="lazy"`.
- Keep as non-interactive layer; do not block clicks.

**Asset:** `/assets/decors/cubo.png` (or `/assets/decors/morado.png`).

---

## G) Gallery — full-screen scroll snap (content unchanged)
- Wrap top-level sections in a container with snap enabled.
- Mark the Gallery section as its own snap region.
- Scope snapping to container so the rest of page scrolls normally.

**CSS:** `/assets/css/components/snap.css`
```css
.snap-container{scroll-snap-type:y mandatory}
.snap-section{min-height:100svh;scroll-snap-align:start;scroll-snap-stop:always}
```
**HTML:**
```html
<main class="snap-container">
  <!-- …other sections… -->
  <section id="gallery" class="snap-section"> …keep current gallery… </section>
  <!-- …other sections… -->
</main>
```

---

## H) Artist Testimonials — interlink-only stub (new)
**Goal now:** Provide anchors and routes for internal linking + sitemap; populate real content later.

**Option 1 (preferred):** dedicated page `/testimonials/`
```html
<!-- /testimonials/index.html (minimal stub) -->
<section id="testimonials" class="layout-narrow">
  <header class="stack center">
    <h1>Artist Testimonials</h1>
    <p class="sub">What artists say about Chocolate & Art (15+ years, nationwide).</p>
  </header>
  <!-- content to be added later -->
</section>
```

**Option 2:** anchor on Artists page `/artists#testimonials`
```html
<!-- on /artists page -->
<section id="testimonials"> <!-- interlink target; content later --></section>
```

**Interlink-only snippet (use wherever relevant):**
```html
<a class="cta" href="/testimonials/">Read artist testimonials</a>
<!-- or -->
<a class="cta" href="/artists#testimonials">Artist testimonials</a>
```
> Keep the CTA inside `.card` footers, the About section, and the Gallery intro paragraph.

**Sitemap instruction:** see Section J.

---

## I) Interstitial images (art + people) between scrolls
- **Frequency:** 1–2 per page max (keep pages light and fast).
- **Placement:** between major sections (e.g., after Hero, between About→Events, between Artists→Gallery). Use the **`bg-box`** wrapper for consistent framing.
- **Files:** place assets under `/assets/images/interstitials/`.
- **Performance:**
  - Use `loading="lazy"` and `decoding="async"`.
  - Provide responsive `srcset` (e.g., 960 / 1440 / 1920px widths).
  - Prefer AVIF/WebP + PNG/JPEG fallback.
  - Alt text should describe **the scene** (people at show, artwork detail), not aesthetics.

**Partial include (example):** `/partials/interstitial-photo.html`
```html
<figure class="bg-box interstitial">
  <img
    src="/assets/images/interstitials/people-960.avif"
    srcset="/assets/images/interstitials/people-960.avif 960w,
            /assets/images/interstitials/people-1440.avif 1440w,
            /assets/images/interstitials/people-1920.avif 1920w"
    sizes="(max-width: 960px) 92vw, 1200px"
    alt="Guests enjoying local art and chocolate at the show"
    loading="lazy" decoding="async">
  <figcaption class="sr-only">Candid photo from a recent Chocolate & Art Show</figcaption>
</figure>
```

---

## J) Interlinking — page graph (very important)
Use descriptive anchors, not generic “click here.” All links should be crawlable (no JS-only routes).

- **Home →** Tickets (/tickets/dallas), Artists (/artists), Gallery (/gallery/dallas), **Testimonials** (/testimonials or /artists#testimonials), About (/about).
- **About →** Events & Cities, **Testimonials**, Contact.
- **Events & Cities →** specific city tickets, Gallery (that city), Artists.
- **Tickets →** Gallery (city), **Testimonials**, FAQ (if present), Contact.
- **Artists →** **Testimonials** (anchor), Gallery, Contact (booking), About.
- **Gallery →** Tickets (city), **Testimonials**, Artists.
- **Testimonials →** Artists, Tickets, Gallery.
- **Contact →** Tickets, Artists.

**In-card CTA copy (short, consistent):**
- Tickets: “Get Dallas tickets →”
- Artists: “Meet the artists →”
- Gallery: “See the vibe →”
- Testimonials: “Why artists love us →”
- About: “Our story →”

---

## K) Sitemap updates (XML + HTML)
**XML** (`/sitemap.xml`) — add entries:
```xml
<url><loc>https://chocolateandartshow.com/testimonials/</loc><changefreq>weekly</changefreq><priority>0.7</priority></url>
<!-- If using anchor instead of page, keep /artists and ensure #testimonials is reachable, but do not list the hash in XML. -->
```
Ensure existing routes are present: `/`, `/about`, `/events`, `/tickets/dallas`, `/artists`, `/gallery/dallas`, `/contact`.

**HTML** (footer/site map block if present): add **Testimonials** link to match the nav.

---

## L) Acceptance criteria
- All boxes visually match **BW Classico** card spec (straight edges, subtle depth, neon hover), no accordion behaviors remain.
- Nav keeps neon look, logo intact, colors sourced from tokens.
- Gallery is its own **full-screen** snap section; scroll feel is smooth, no layout shifts.
- **Testimonials** route or anchor exists and is linked from Home, About, Artists, Gallery, Tickets, and footer.
- Interstitial images appear **at most 1–2 per page**, lazy-load correctly, and do not regress Lighthouse metrics.
- `sitemap.xml` includes **Testimonials** (or the parent page if using an anchor) and all core routes.

---

## M) Quick change checklist
- [ ] Add colors.json + map to `:root` in theme.css (with branding comment).
- [ ] Convert all boxes to `.card` / `.card-grid` and remove accordion CSS/JS.
- [ ] Tokenize nav colors; confirm logo & menu items (add **Testimonials**).
- [ ] Drop pedestal asset under `/assets/decors/` and wire behind hero CTAs.
- [ ] Wrap sections with `.snap-container`; mark `#gallery` with `.snap-section`.
- [ ] Create `/testimonials/` page **or** `#testimonials` anchor on `/artists`.
- [ ] Add interstitial photo partial and place 1–2 per page between sections.
- [ ] Update `/sitemap.xml` and footer site map links.
- [ ] Smoke test interlinking paths (no 404s; link text descriptive).

