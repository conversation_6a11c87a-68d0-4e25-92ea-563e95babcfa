<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="bgGrad" cx="50%" cy="50%" r="70%">
      <stop offset="0%" stop-color="#1a0b3a"/>
      <stop offset="100%" stop-color="#040814"/>
    </radialGradient>
    <filter id="neonGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feFlood flood-color="#ff19f3" result="flood"/>
      <feComposite in="flood" in2="SourceAlpha" operator="in" result="mask"/>
      <feGaussianBlur in="mask" stdDeviation="4" result="blurred"/>
      <feMerge>
        <feMergeNode in="blurred"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <filter id="cyanGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feFlood flood-color="#00fff0" result="flood"/>
      <feComposite in="flood" in2="SourceAlpha" operator="in" result="mask"/>
      <feGaussianBlur in="mask" stdDeviation="3" result="blurred"/>
      <feMerge>
        <feMergeNode in="blurred"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Background circle with neon pink glow -->
  <circle cx="50" cy="50" r="45" fill="url(#bgGrad)" stroke="#ff19f3" stroke-width="3" filter="url(#neonGlow)"/>

  <!-- Neon Eventbrite "e" -->
  <text x="50" y="60" text-anchor="middle" font-family="Arial, sans-serif" font-weight="bold" font-size="60" fill="#00fff0" filter="url(#cyanGlow)">e</text>

  <!-- Sparkle accents -->
  <circle cx="20" cy="25" r="1.5" fill="#ff19f3" filter="url(#neonGlow)"/>
  <circle cx="80" cy="30" r="1.2" fill="#00fff0" filter="url(#cyanGlow)"/>
  <circle cx="30" cy="75" r="1.3" fill="#ff19f3" filter="url(#neonGlow)"/>
  <circle cx="70" cy="80" r="1.4" fill="#00fff0" filter="url(#cyanGlow)"/>
</svg>