<svg viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="bgGrad" cx="30%" cy="30%" r="75%">
      <stop offset="0%" stop-color="#001f3f"/>
      <stop offset="100%" stop-color="#000814"/>
    </radialGradient>
    <filter id="neonGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3" result="blur"/>
      <feOffset in="blur" dx="0" dy="0" result="offsetBlur"/>
      <feFlood flood-color="#00e6ff" result="flood"/>
      <feComposite in="flood" in2="offsetBlur" operator="in" result="neon"/>
      <feMerge>
        <feMergeNode in="neon"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  <rect x="0" y="0" width="64" height="64" rx="12" fill="url(#bgGrad)"/>
  <path filter="url(#neonGlow)" d="M39.8 20H34v-4c0-1.1.9-2 2-2h4v-6h-5c-4.4 0-8 3.6-8 8v4h-4v6h4v16h6V26h4l1.8-6z" fill="#00e6ff"/>
  <path d="M39.8 20H34v-4c0-1.1.9-2 2-2h4v-6h-5c-4.4 0-8 3.6-8 8v4h-4v6h4v16h6V26h4l1.8-6z" fill="#ffffff" fill-opacity="0.85"/>
</svg>