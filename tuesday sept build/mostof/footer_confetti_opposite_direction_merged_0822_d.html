<footer class="site-footer" id="site-footer" role="contentinfo">
  <div class="footer-inner">
    <a class="brand" href="#" aria-label="Chocolate & Art Show home">
      <img src="/assets/images/choco-logo.png" width="28" height="28" alt="Chocolate & Art Show" />
      <span>Chocolate & Art Show</span>
    </a>
    <nav class="footer-nav" aria-label="Footer navigation">
      <a href="#">About</a>
      <a href="#">Artists</a>
      <a href="#">Tickets</a>
      <a href="#">Gallery</a>
      <a href="#">Contact</a>
    </nav>
    <nav class="footer-social" aria-label="Social links">
      <a href="#" aria-label="Instagram">IG</a>
      <a href="#" aria-label="Facebook">FB</a>
      <a href="#" aria-label="Interactive art" data-easter-egg="kusama">•</a>
    </nav>
    <div class="footer-celebrate">
      <button class="btn-modern" id="footer-confetti-trigger" aria-label="Celebrate Dallas">
        <span class="btn-modern__inner">Celebrate Dallas</span>
      </button>
    </div>
  </div>
  <div class="footer-legal">
    <small>© 2025 Chocolate & Art Show · 21+ (ID required) · Lofty Spaces · Sept 18–19</small>
  </div>
</footer>
<canvas id="confetti-canvas" class="confetti-canvas" aria-hidden="true"></canvas>
<style>
  .site-footer{background:#0a0a0a;color:#eaeaea;padding:24px 16px}
  .footer-inner{display:flex;flex-wrap:wrap;align-items:center;gap:16px;justify-content:space-between;max-width:1200px;margin:0 auto}
  .brand{display:inline-flex;align-items:center;gap:8px;color:inherit;text-decoration:none;font-weight:600}
  .footer-nav,.footer-social{display:flex;gap:16px;flex-wrap:wrap}
  .footer-nav a,.footer-social a{color:#cfcfcf;text-decoration:none;font-size:.95rem}
  .footer-nav a:hover,.footer-social a:hover{color:#fff}
  .footer-celebrate{margin-left:auto}
  .footer-legal{max-width:1200px;margin:10px auto 0;padding-top:8px;border-top:1px solid #ffffff15;color:#bdbdbd;font-size:.85rem;text-align:center}
  .btn-modern{position:relative;display:inline-grid;place-items:center;padding:0;height:46px;min-width:220px;border:1px solid rgba(255,255,255,.12);background:linear-gradient(180deg,#222,#0f0f0f);color:#fff;cursor:pointer;user-select:none;overflow:hidden;border-radius:0;box-shadow:0 12px 24px rgba(0,0,0,.35),0 0 0 1px rgba(255,255,255,.06),inset 0 1px 0 rgba(255,255,255,.08),inset 0 -1px 0 rgba(0,0,0,.6);transition:box-shadow .18s ease,transform .12s ease,background .18s ease}
  .btn-modern:focus-visible{outline:2px solid #9d7cff;outline-offset:2px}
  .btn-modern__inner{position:relative;z-index:2;padding:0 18px;font-weight:800;letter-spacing:.02em}
  .btn-modern::after{content:"";position:absolute;inset:0;background:linear-gradient(180deg,rgba(255,255,255,.06),transparent 40% 60%,rgba(255,255,255,.02));mix-blend-mode:screen;pointer-events:none}
  .btn-modern:hover{box-shadow:0 16px 28px rgba(0,0,0,.4),0 0 0 1px rgba(255,255,255,.08),inset 0 1px 0 rgba(255,255,255,.1),inset 0 -1px 0 rgba(0,0,0,.65)}
  .btn-modern:active{transform:translateY(1px);box-shadow:0 8px 16px rgba(0,0,0,.35),0 0 0 1px rgba(255,255,255,.1),inset 0 1px 0 rgba(255,255,255,.06),inset 0 -1px 0 rgba(0,0,0,.5)}
  .confetti-canvas{position:fixed;inset:0;pointer-events:none;z-index:9999;display:block}
  @media (prefers-reduced-motion: reduce){.btn-modern{transition:none}}
</style>
<script>
(function(){var b=document.getElementById("footer-confetti-trigger"),c=document.getElementById("confetti-canvas");if(!b||!c)return;var d=window.matchMedia&&window.matchMedia("(prefers-reduced-motion: reduce)").matches,e=navigator.connection&&navigator.connection.saveData===true,f=navigator.deviceMemory&&Number(navigator.deviceMemory)<=2;if(d||e||f)return;var g=c.getContext("2d");function h(){c.width=window.innerWidth;c.height=window.innerHeight}h();window.addEventListener("resize",h);function i(j,k){var l=Math.PI/2+Math.random()*Math.PI,m=6+Math.random()*6;this.x=j;this.y=k;this.vx=Math.cos(l)*m;this.vy=Math.sin(l)*m-4;this.size=6+Math.random()*6;this.rotation=Math.random()*Math.PI;this.vr=(Math.random()-.5)*.2;this.color=["#f8e16b","#f6a6ff","#8ad5ff","#ff8a8a","#9d7cff","#ffffff"][Math.random()*6|0];this.alpha=1;this.shape=Math.random()<.5?"rect":"tri"}i.prototype.update=function(){this.vy+=.3;this.vx*=.99;this.vy*=.99;this.x+=this.vx;this.y+=this.vy;this.rotation+=this.vr;this.alpha*=.985};i.prototype.draw=function(){g.globalAlpha=Math.max(this.alpha,0);g.save();g.translate(this.x,this.y);g.rotate(this.rotation);g.fillStyle=this.color;if(this.shape==="rect"){g.fillRect(-this.size/2,-this.size/2,this.size,this.size*.7)}else{g.beginPath();g.moveTo(0,-this.size/2);g.lineTo(this.size/2,this.size/2);g.lineTo(-this.size/2,this.size/2);g.closePath();g.fill()}g.restore()};function n(o){var p=performance.now(),q=o&&o.duration||900,r=[],s=o&&o.particleCount||160,t=o&&o.x||c.width/2,u=o&&o.y||c.height-80;for(var v=0;v<s;v++)r.push(new i(t,u));function w(x){var y=x-p;g.clearRect(0,0,c.width,c.height);for(var z=0;z<r.length;z++){r[z].update();r[z].draw()}if(y<q){requestAnimationFrame(w)}else{g.clearRect(0,0,c.width,c.height)}}requestAnimationFrame(w)}b.addEventListener("click",function(){var a=b.getBoundingClientRect(),x=a.left+a.width/2,y=a.top+a.height/2;n({x:x,y:Math.min(y,window.innerHeight-80),duration:1000,particleCount:180})})})();
</script>
